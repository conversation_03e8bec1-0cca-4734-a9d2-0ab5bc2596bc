/* common-screens.css */
/* Gemeinsame Stile für alle Screen-Overlays */

.screen-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 16px 10px 16px;
  background: none;
  color: var(--standard-text-color);
  background-color: var(--standard-background-color);
}

.screen-header h1 {
  color: var(--standard-text-color);
  font-size: 1.2rem;
  line-height: 2rem;
  font-weight: bold;
  margin: 0 10px 0 0;
  text-shadow: none;
}

.screen-header .back-btn {
  background: none;
  border: none;
  color: var(--standard-text-color);
  font-size: 1.5rem;
  cursor: pointer;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.12));
  display: flex;
  align-items: center;
  padding: 0 4px 0 0;
}

.screen-header .back-btn svg,
.screen-header .back-btn img {
  display: inline;
  vertical-align: middle;
}

.screen-header .header-right {
  margin-left: auto;
}
