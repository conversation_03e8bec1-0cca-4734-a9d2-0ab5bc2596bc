{"trainerTypes": {"Ace_Trainer": {"displayName": "<PERSON>", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Ace_Trainer1_Battle.png", "mapSprite": "Ace_Trainer1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Ace_Trainer2_Battle.png", "mapSprite": "Ace_Trainer2_Map.png"}, {"id": 3, "gender": "female", "battleSprite": "Ace_Trainer3_Battle.png", "mapSprite": "Ace_Trainer3_Map.png"}, {"id": 4, "gender": "female", "battleSprite": "Ace_Trainer4_Battle.png", "mapSprite": "Ace_Trainer4_Map.png"}, {"id": 5, "gender": "male", "battleSprite": "Ace_Trainer5_Battle.png", "mapSprite": "Ace_Trainer5_Map.png"}, {"id": 6, "gender": "male", "battleSprite": "Ace_Trainer6_Battle.png", "mapSprite": "Ace_Trainer6_Map.png"}, {"id": 7, "gender": "male", "battleSprite": "Ace_Trainer7_Battle.png", "mapSprite": "Ace_Trainer7_Map.png"}, {"id": 8, "gender": "male", "battleSprite": "Ace_Trainer8_Battle.png", "mapSprite": "Ace_Trainer8_Map.png"}]}, "preferences": ["Dragon", "Steel"], "levelRange": {"min": 55, "max": 80}}, "Aroma_Lady": {"displayName": "Aroma Lady", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Aroma_Lady1_Battle.png", "mapSprite": "Aroma_Lady1_Map.png"}]}, "preferences": ["Grass"], "levelRange": {"min": 20, "max": 35}}, "Backpacker": {"displayName": "Backpacker", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Backpacker_F1_Battle.png", "mapSprite": "Backpacker_f1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Backpacker_M1_Battle.png", "mapSprite": "Backpacker_m1_Map.png"}]}, "preferences": ["Normal", "Ground"], "levelRange": {"min": 18, "max": 30}}, "Battle_Girl": {"displayName": "Battle Girl", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Battle_Girl1_Battle.png", "mapSprite": "Battle_Girl1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Battle_Girl2_Battle.png", "mapSprite": "Battle_Girl2_Map.png"}]}, "preferences": ["Fighting"], "levelRange": {"min": 30, "max": 50}}, "Beauty": {"displayName": "Beauty", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Beauty1_Battle.png", "mapSprite": "Beauty1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Beauty2_Battle.png", "mapSprite": "Beauty2_Map.png"}, {"id": 3, "gender": "female", "battleSprite": "Beauty3_Battle.png", "mapSprite": "Beauty3_Map.png"}]}, "preferences": ["Fairy"], "levelRange": {"min": 15, "max": 30}}, "Bird_Keeper": {"displayName": "<PERSON> Keeper", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Bird_Keeper1_Battle.png", "mapSprite": "Bird_Keeper1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Bird_Keeper2_Battle.png", "mapSprite": "Bird_Keeper2_Map.png"}]}, "preferences": ["Flying"], "levelRange": {"min": 15, "max": 28}}, "Black_Belt": {"displayName": "Black Belt", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Black_Belt1_Battle.png", "mapSprite": "Black_Belt1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Black_Belt2_Battle.png", "mapSprite": "Black_Belt2_Map.png"}, {"id": 3, "gender": "male", "battleSprite": "Black_Belt3_Battle.png", "mapSprite": "Black_Belt3_Map.png"}]}, "preferences": ["Fighting"], "levelRange": {"min": 30, "max": 50}}, "Bug_Catcher": {"displayName": "<PERSON><PERSON> Catcher", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Bug_Catcher1_Battle.png", "mapSprite": "Bug_Catcher1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Bug_Catcher2_Battle.png", "mapSprite": "Bug_Catcher2_Map.png"}]}, "preferences": ["Bug"], "levelRange": {"min": 5, "max": 15}}, "Camper": {"displayName": "Camper", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Camper1_Battle.png", "mapSprite": "Camper1_Map.png"}]}, "preferences": ["Grass", "Normal"], "levelRange": {"min": 10, "max": 20}}, "Firebreather": {"displayName": "Firebreather", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Firebreather1_Battle.png", "mapSprite": "Firebreather1_Map.png"}]}, "preferences": ["Fire"], "levelRange": {"min": 28, "max": 45}}, "Fisherman": {"displayName": "Fisherman", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Fisherman1_Battle.png", "mapSprite": "Fisherman1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Fisherman2_Battle.png", "mapSprite": "Fisherman2_Map.png"}]}, "preferences": ["Water"], "levelRange": {"min": 12, "max": 25}}, "Gentleman": {"displayName": "Gentleman", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Gentleman1_Battle.png", "mapSprite": "Gentleman1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Gentleman2_Battle.png", "mapSprite": "Gentleman2_Map.png"}, {"id": 3, "gender": "male", "battleSprite": "Gentleman3_Battle.png", "mapSprite": "Gentleman3_Map.png"}]}, "preferences": ["Ice", "Fairy"], "levelRange": {"min": 25, "max": 40}}, "Guitarist": {"displayName": "Guitarist", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Guitarist1_Battle.png", "mapSprite": "Guitarist1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Guitarist2_Battle.png", "mapSprite": "Guitarist2_Map.png"}]}, "preferences": ["Electric"], "levelRange": {"min": 25, "max": 42}}, "Hiker": {"displayName": "<PERSON><PERSON>", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Hiker1_Battle.png", "mapSprite": "Hiker1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Hiker2_Battle.png", "mapSprite": "Hiker2_Map.png"}]}, "preferences": ["Rock", "Ground"], "levelRange": {"min": 18, "max": 32}}, "Kimono_Girl": {"displayName": "<PERSON><PERSON>", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "<PERSON><PERSON>_Girl1_Battle.png", "mapSprite": "Kimono_Girl1_Map.png"}]}, "preferences": ["Fairy", "Bug"], "levelRange": {"min": 32, "max": 50}}, "Lady": {"displayName": "Lady", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Lady1_Battle.png", "mapSprite": "Lady1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Lady2_Battle.png", "mapSprite": "Lady2_Map.png"}]}, "preferences": ["Ice", "Fairy"], "levelRange": {"min": 25, "max": 40}}, "Lass": {"displayName": "<PERSON><PERSON>", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Lass1_Battle.png", "mapSprite": "Lass1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Lass2_Battle.png", "mapSprite": "Lass2_Map.png"}, {"id": 3, "gender": "female", "battleSprite": "Lass3_Battle.png", "mapSprite": "Lass3_Map.png"}]}, "preferences": ["Normal", "Grass"], "levelRange": {"min": 8, "max": 18}}, "Medium": {"displayName": "Medium", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Medium1_Battle.png", "mapSprite": "Medium1_Map.png"}]}, "preferences": ["Ghost"], "levelRange": {"min": 25, "max": 45}}, "Parasol_Lady": {"displayName": "Parasol Lady", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Parasol_Lady1_Battle.png", "mapSprite": "Parasol_Lady1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Parasol_Lady2_Battle.png", "mapSprite": "Parasol_Lady2_Map.png"}]}, "preferences": ["Grass", "Water"], "levelRange": {"min": 22, "max": 35}}, "Picnicker": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Picnicker1_Battle.png", "mapSprite": "Picnicker1_Map.png"}]}, "preferences": ["Grass", "Normal"], "levelRange": {"min": 10, "max": 20}}, "Poke_Fan": {"displayName": "<PERSON><PERSON>-<PERSON>", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Poke_Fan1_Battle.png", "mapSprite": "Poke_Fan1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Poke_Fan2_Battle.png", "mapSprite": "Poke_Fan2_Map.png"}, {"id": 3, "gender": "male", "battleSprite": "Poke_Fan3_Battle.png", "mapSprite": "Poke_Fan3_Map.png"}, {"id": 4, "gender": "male", "battleSprite": "Poke_Fan4_Battle.png", "mapSprite": "Poke_Fan4_Map.png"}]}, "preferences": ["Electric", "Normal"], "levelRange": {"min": 20, "max": 38}}, "Poke_Maniac": {"displayName": "<PERSON><PERSON>", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Poke_Maniac1_Battle.png", "mapSprite": "Poke_Maniac1_Map.png"}]}, "preferences": ["Normal"], "levelRange": {"min": 25, "max": 55}}, "Pokemon_Breeder": {"displayName": "<PERSON><PERSON><PERSON>", "sprites": {"variants": [{"id": 1, "gender": "female", "mapSprite": "Pokemon_Breeder1_Map.png", "battleSprite": "Pokemon_Breeder1_Battle.png"}, {"id": 2, "gender": "female", "mapSprite": "Pokemon_Breeder2_Map.png", "battleSprite": "Pokemon_Breeder2_Battle.png"}, {"id": 3, "gender": "male", "mapSprite": "Pokemon_Breeder3_Map.png", "battleSprite": "Pokemon_Breeder3_Battle.png"}, {"id": 4, "gender": "male", "mapSprite": "Pokemon_Breeder4_Map.png", "battleSprite": "Pokemon_Breeder4_Battle.png"}]}, "preferences": ["Normal", "Fairy"], "levelRange": {"min": 30, "max": 60}}, "Pokemon_Ranger": {"displayName": "Pokemon Ranger", "sprites": {"variants": [{"id": 1, "gender": "female", "mapSprite": "Pokemon_Ranger1_Map.png", "battleSprite": "Pokemon_Ranger1_Battle.png"}, {"id": 2, "gender": "female", "mapSprite": "Pokemon_Ranger2_Map.png", "battleSprite": "Pokemon_Ranger2_Battle.png"}, {"id": 3, "gender": "male", "mapSprite": "Pokemon_Ranger3_Map.png", "battleSprite": "Pokemon_Ranger3_Battle.png"}, {"id": 4, "gender": "male", "mapSprite": "Pokemon_Ranger4_Map.png", "battleSprite": "Pokemon_Ranger4_Battle.png"}]}, "preferences": ["Flying", "Grass"], "levelRange": {"min": 35, "max": 70}}, "Preschooler": {"displayName": "Preschooler", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Preschooler_F1_Battle.png", "mapSprite": "Preschooler_f1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Preschooler_M1_Battle.png", "mapSprite": "Preschooler_m1_Map.png"}]}, "preferences": ["Normal", "Fairy"], "levelRange": {"min": 1, "max": 8}}, "Psychic": {"displayName": "Psychic", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Psychic_F1_Battle.png", "mapSprite": "Psychic_F1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Psychic_F2_Battle.png", "mapSprite": "Psychic_F2_Map.png"}, {"id": 3, "gender": "male", "battleSprite": "Psychic_M1_Battle.png", "mapSprite": "Psychic_m1_Map.png"}, {"id": 4, "gender": "male", "battleSprite": "Psychic_M2_Battle.png", "mapSprite": "Psychic_M2_Map.png"}]}, "preferences": ["Psychic"], "levelRange": {"min": 28, "max": 48}}, "Rocket_Grunt": {"displayName": "<PERSON>", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Rocket_Grunt1_Battle.png", "mapSprite": "Rocket_Grunt1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Rocket_Grunt2_Battle.png", "mapSprite": "Rocket_Grunt2_Map.png"}]}, "preferences": ["Poison", "Dark"], "levelRange": {"min": 45, "max": 65}}, "Sailor": {"displayName": "Sailor", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Sailor1_Battle.png", "mapSprite": "Sailor1_Map.png"}]}, "preferences": ["Water", "Fighting"], "levelRange": {"min": 20, "max": 35}}, "School_Kid": {"displayName": "School Kid", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "School_Kid1_Battle.png", "mapSprite": "School_Kid1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "School_Kid2_Battle.png", "mapSprite": "School_Kid2_Map.png"}, {"id": 3, "gender": "male", "battleSprite": "School_Kid3_Battle.png", "mapSprite": "School_Kid3_Map.png"}, {"id": 4, "gender": "male", "battleSprite": "School_Kid4_Battle.png", "mapSprite": "School_Kid4_Map.png"}, {"id": 5, "gender": "male", "battleSprite": "School_Kid5_Battle.png", "mapSprite": "School_Kid5_Map.png"}]}, "preferences": ["Normal", "Electric"], "levelRange": {"min": 3, "max": 12}}, "Scientist": {"displayName": "Scientist", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Scientist_F1_Battle.png", "mapSprite": "Scientist_F1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Scientist_M1_Battle.png", "mapSprite": "Scientist_M1_Map.png"}, {"id": 3, "gender": "male", "battleSprite": "Scientist_M2_Battle.png", "mapSprite": "Scientist_M2_Map.png"}]}, "preferences": ["Electric", "Poison"], "levelRange": {"min": 35, "max": 55}}, "Socialite": {"displayName": "Socialite", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Socialite1_Battle.png", "mapSprite": "Socialite1_Map.png"}, {"id": 2, "gender": "female", "battleSprite": "Socialite2_Battle.png", "mapSprite": "Socialite2_Map.png"}]}, "preferences": ["Fairy", "Normal"], "levelRange": {"min": 30, "max": 58}}, "Teacher": {"displayName": "Teacher", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Teacher1_Battle.png", "mapSprite": "Teacher1_Map.png"}]}, "preferences": ["Psychic", "Normal"], "levelRange": {"min": 25, "max": 40}}, "Veteran": {"displayName": "Veteran", "sprites": {"variants": [{"id": 1, "gender": "female", "battleSprite": "Veteran_F1_Battle.png", "mapSprite": "Veteran_F1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Veteran_M1_Battle.png", "mapSprite": "Veteran_M1_Map.png"}]}, "preferences": ["Fighting", "Steel"], "levelRange": {"min": 60, "max": 80}}, "Youngster": {"displayName": "Youngster", "sprites": {"variants": [{"id": 1, "gender": "male", "battleSprite": "Youngster1_Battle.png", "mapSprite": "Youngster1_Map.png"}, {"id": 2, "gender": "male", "battleSprite": "Youngster2_Battle.png", "mapSprite": "Youngster2_Map.png"}, {"id": 3, "gender": "male", "battleSprite": "Youngster3_Battle.png", "mapSprite": "Youngster3_Map.png"}]}, "preferences": ["Normal", "Fighting"], "levelRange": {"min": 8, "max": 18}}}}