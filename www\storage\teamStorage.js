// storage/teamStorage.js
// Module for managing the player's Pokémon team in storage
// This is a wrapper around the PokemonManager for backward compatibility

import { logger } from '../utils/logger.js';
import { pokemonManager } from '../services/pokemon-manager.js';

// Maximum team size
const MAX_TEAM_SIZE = 6;

/**
 * Load the player's team
 * @returns {Promise<Array>} - The team Pokémon
 */
export async function loadTeam() {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Return the team Pokemon
    return pokemonManager.getTeamPokemon();
  } catch (e) {
    logger.error('Error loading team:', e);
    return [];
  }
}

/**
 * Save the team
 * @param {Array} team - The team to save
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function saveTeam(team) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Update all Pokemon in the team
    for (const pokemon of team) {
      await pokemonManager.updatePokemon(pokemon);
    }

    // Update the team IDs
    pokemonManager.teamIds = new Set(team.map(p => p.id));

    // Save the team
    return await pokemonManager.saveTeam();
  } catch (e) {
    logger.error('Error saving team:', e);
    return false;
  }
}

/**
 * Add a Pokémon to the team
 * @param {Object} pokemon - The Pokémon to add
 * @returns {Promise<{success: boolean, message: string}>} - Result with success status and message
 */
export async function addToTeam(pokemon) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Import the experience system to ensure proper XP initialization
    const { experienceSystem, getExpForLevel } = await import('../services/experience-system.js');

    // Make sure the Pokemon has proper experience value for its level
    if (typeof pokemon.level === 'number' && pokemon.level > 0) {
      // If experience is missing or negative, initialize it properly
      if (typeof pokemon.experience !== 'number' || pokemon.experience < 0) {
        // Get the base experience for the current level
        const curve = experienceSystem.getExpCurve(pokemon);
        const currentLevelExp = getExpForLevel(pokemon.level, curve);

        logger.debug(`Fixing Pokemon ${pokemon.name} experience: ${pokemon.experience} -> ${currentLevelExp}`);
        pokemon.experience = currentLevelExp;
      }
    }

    // Make sure the Pokemon is in the manager
    await pokemonManager.addPokemon(pokemon);

    // Add to team
    const result = await pokemonManager.addToTeam(pokemon.id);

    return {
      success: result.success,
      message: result.message
    };
  } catch (e) {
    logger.error('Error adding Pokémon to team:', e);
    return {
      success: false,
      message: 'An error occurred while adding to team'
    };
  }
}

/**
 * Remove a Pokémon from the team
 * @param {string} id - The ID of the Pokémon to remove
 * @returns {Promise<{success: boolean, message: string, pokemon: Object|null}>} - Result with success status, message and removed Pokémon
 */
export async function removeFromTeam(id) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Remove from team
    const result = await pokemonManager.removeFromTeam(id);

    return result;
  } catch (e) {
    logger.error('Error removing Pokémon from team:', e);
    return {
      success: false,
      message: 'An error occurred while removing from team',
      pokemon: null
    };
  }
}

/**
 * Check if the team is full
 * @returns {Promise<boolean>} - Whether the team is full
 */
export async function isTeamFull() {
  // Initialize the Pokemon manager if not already initialized
  await pokemonManager.initialize();

  // Get team Pokemon
  const team = pokemonManager.getTeamPokemon();
  return team.length >= MAX_TEAM_SIZE;
}

/**
 * Get the current number of Pokémon in the team
 * @returns {Promise<number>} - The number of Pokémon in the team
 */
export async function getTeamCount() {
  // Initialize the Pokemon manager if not already initialized
  await pokemonManager.initialize();

  // Get team Pokemon
  const team = pokemonManager.getTeamPokemon();
  return team.length;
}

/**
 * Get the buddy Pokémon (first Pokémon in the team)
 * @returns {Promise<Object|null>} - The buddy Pokémon or null if team is empty
 */
export async function getBuddyPokemon() {
  // Initialize the Pokemon manager if not already initialized
  await pokemonManager.initialize();

  // Get team Pokemon
  const team = pokemonManager.getTeamPokemon();
  return team.length > 0 ? team[0] : null;
}

/**
 * Move a Pokémon to the first position in the team (making it the buddy)
 * @param {string} id - The ID of the Pokémon to make buddy
 * @returns {Promise<{success: boolean, message: string}>} - Result with success status and message
 */
export async function makePokemonBuddy(id) {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Make buddy
    return await pokemonManager.makePokemonBuddy(id);
  } catch (e) {
    logger.error('Error making Pokémon buddy:', e);
    return {
      success: false,
      message: 'An error occurred while updating buddy'
    };
  }
}
