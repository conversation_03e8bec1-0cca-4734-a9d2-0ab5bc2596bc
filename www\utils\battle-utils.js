// utils/battle-utils.js
// Shared utility functions for battle screens

import { logger } from './logger.js';
import { getGermanPokemonName } from './pokemon-display-names.js';

/**
 * Calculate experience progress for a Pokemon
 * @param {Object} pokemon - The Pokemon object
 * @param {number} additionalExp - Additional experience to show (for animation)
 * @returns {Object} - Experience progress data
 */
export function calculateExpProgress(pokemon, additionalExp = 0) {
  try {
    // Default values
    const result = {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };

    // Ensure we have a valid Pokemon with level
    if (!pokemon || typeof pokemon.level !== 'number') {
      return result;
    }

    // Import experience system functions dynamically to avoid circular dependencies
    import('../services/experience-system.js').then(({ experienceSystem, getExpForLevel }) => {
      const currentLevel = pokemon.level;
      const currentExp = pokemon.experience || 0;
      
      // Get experience curve for this Pokemon
      const curve = experienceSystem.getExpCurve(pokemon);
      
      // Calculate experience thresholds
      const currentLevelExp = getExpForLevel(currentLevel, curve);
      const nextLevelExp = getExpForLevel(currentLevel + 1, curve);
      
      // Calculate progress within current level
      const expInCurrentLevel = Math.max(0, currentExp - currentLevelExp);
      const expNeededForNextLevel = nextLevelExp - currentLevelExp;
      const progressPercentage = Math.min(100, (expInCurrentLevel / expNeededForNextLevel) * 100);
      
      // Calculate new progress with additional experience
      const newTotalExp = currentExp + additionalExp;
      const newExpInCurrentLevel = Math.max(0, newTotalExp - currentLevelExp);
      const newExpPercentage = Math.min(100, (newExpInCurrentLevel / expNeededForNextLevel) * 100);
      
      // Check if Pokemon will level up
      const willLevelUp = newTotalExp >= nextLevelExp;
      
      Object.assign(result, {
        currentExp,
        currentLevelExp,
        nextLevelExp,
        expInCurrentLevel,
        expNeededForNextLevel,
        progressPercentage,
        newExpPercentage,
        willLevelUp
      });
    }).catch(e => {
      logger.error('Error calculating experience progress:', e);
    });

    return result;
  } catch (e) {
    logger.error('Error in calculateExpProgress:', e);
    return {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };
  }
}

/**
 * Update type badges for a Pokemon
 * @param {HTMLElement} container - Container element for type badges
 * @param {Object} pokemon - Pokemon data
 */
export function updateTypeBadges(container, pokemon) {
  if (!container || !pokemon) return;

  const types = Array.isArray(pokemon.types) ? pokemon.types : ['Normal'];
  
  let typeBadgesHtml = '';
  types.forEach(type => {
    typeBadgesHtml += `
      <div class='pokemon-type-badge-group'>
        <span class='pokemon-type-badge' style='background: var(--type-${type.toLowerCase()})'>${type}</span>
      </div>
    `;
  });

  container.innerHTML = typeBadgesHtml;
}

/**
 * Update experience bar for a Pokemon
 * @param {HTMLElement} container - Container element for XP bar
 * @param {Object} pokemon - Pokemon data
 * @param {number} additionalExp - Additional experience to show
 */
export function updateExpBar(container, pokemon, additionalExp = 0) {
  if (!container || !pokemon) return;

  const expProgress = calculateExpProgress(pokemon, additionalExp);
  
  container.innerHTML = `
    <div class="pokemon-exp-bar">
      <div class="pokemon-exp-fill" style="width: ${expProgress.progressPercentage}%"></div>
      ${additionalExp > 0 ? `<div class="pokemon-exp-new" style="width: ${expProgress.newExpPercentage - expProgress.progressPercentage}%"></div>` : ''}
    </div>
    <div class="pokemon-exp-text">XP: ${pokemon.experience || 0}</div>
  `;
}

/**
 * Update health bar for a Pokemon
 * @param {HTMLElement} healthFill - Health fill element
 * @param {HTMLElement} healthText - Health text element
 * @param {number} healthPercentage - Health percentage (0-100)
 */
export function updateHealthBar(healthFill, healthText, healthPercentage) {
  if (!healthFill || !healthText) return;

  const clampedHealth = Math.max(0, Math.min(100, healthPercentage));
  
  healthFill.style.width = `${clampedHealth}%`;
  healthText.textContent = `${Math.round(clampedHealth)}%`;
  
  // Add low health class if health is below 25%
  if (clampedHealth <= 25) {
    healthFill.classList.add('low-health');
  } else {
    healthFill.classList.remove('low-health');
  }
}

/**
 * Create Pokemon indicators (6 red/gray balls)
 * @param {Array} pokemonStatus - Array of Pokemon status objects
 * @returns {string} - HTML for indicators
 */
export function generatePokemonIndicators(pokemonStatus) {
  let html = '';
  for (let i = 0; i < 6; i++) {
    const isDefeated = pokemonStatus[i] && pokemonStatus[i].defeated;
    const className = isDefeated ? 'pokemon-indicator defeated' : 'pokemon-indicator';
    html += `<div class="${className}"></div>`;
  }
  return html;
}

/**
 * Get display name for Pokemon (German if available)
 * @param {Object} pokemon - Pokemon object
 * @returns {string} - Display name
 */
export function getPokemonDisplayName(pokemon) {
  if (!pokemon) return 'Unknown';
  return getGermanPokemonName(pokemon) || pokemon.name || 'Unknown';
}

/**
 * Get Pokemon image URL
 * @param {Object} pokemon - Pokemon object
 * @returns {string} - Image URL
 */
export function getPokemonImageUrl(pokemon) {
  if (!pokemon) return '';
  return pokemon.image || pokemon.image_url || `./src/PokemonSprites/${pokemon.dex_number}.png`;
}

/**
 * Animate element with CSS class
 * @param {HTMLElement} element - Element to animate
 * @param {string} animationClass - CSS animation class
 * @param {number} duration - Animation duration in ms
 * @returns {Promise<void>}
 */
export function animateElement(element, animationClass, duration = 1000) {
  return new Promise((resolve) => {
    if (!element) {
      resolve();
      return;
    }

    element.classList.add(animationClass);
    
    setTimeout(() => {
      element.classList.remove(animationClass);
      resolve();
    }, duration);
  });
}

/**
 * Show temporary notification
 * @param {HTMLElement} container - Container for notification
 * @param {string} message - Notification message
 * @param {string} className - CSS class for styling
 * @param {number} duration - Duration in ms
 * @returns {Promise<void>}
 */
export function showNotification(container, message, className = 'notification', duration = 3000) {
  return new Promise((resolve) => {
    if (!container) {
      resolve();
      return;
    }

    const notification = document.createElement('div');
    notification.className = className;
    notification.textContent = message;
    notification.style.opacity = '1';

    container.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
      resolve();
    }, duration);
  });
}
