/* fab-submenu.css: Styling für das FAB Submenu */

.fab-submenu {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 24px;
  z-index: 10001;
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* Immer 4 gleiche Spalten */
  grid-template-rows: 1fr; /* Nur eine Zeile */
  justify-items: center; /* Zentriert die Buttons in ihren Zellen */
  align-items: center;
  width: 100%;
  max-width: 100%;
  pointer-events: none;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s 0.3s; /* Verzögere visibility change */
  visibility: hidden; /* Hide completely when not active */
  padding: 0 16px;
  box-sizing: border-box;
}

.fab-submenu.active {
  opacity: 1;
  transform: translateY(0);
  visibility: visible; /* Show when active */
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s; /* Sofortige Sichtbarkeit beim Aktivieren */
}

.fab-submenu-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  border: none;
  outline: none;
  cursor: pointer;
  transition: background 0.2s, filter 0.2s;
  margin: 0;
  pointer-events: auto; /* Enable pointer events only when parent is active */
  font-size: 32px;
  min-width: 0;
  -webkit-tap-highlight-color: transparent; /* Deaktiviert den blauen Highlight-Effekt auf mobilen Geräten */
  -webkit-touch-callout: none; /* Verhindert das Kontextmenü auf iOS */
  -webkit-user-select: none; /* Verhindert Textauswahl */
  user-select: none; /* Verhindert Textauswahl */
}

/* Disable pointer events when submenu is not active */
.fab-submenu:not(.active) .fab-submenu-btn {
  pointer-events: none;
}

#pokedex-submenu-fab {
  background: var(--pinky-red);
  color: #fff;
  grid-column: 3; /* Vierte Spalte */
  grid-row: 1; /* Erste und einzige Zeile */
}

#encounters-submenu-fab {
  background: var(--main-blue);
  color: #fff;
  grid-column: 1; /* Zweite Spalte */
  grid-row: 1; /* Erste und einzige Zeile */
}

#pokemon-caught-fab {
  background: var(--bright-yellow);
  color: #fff;
  grid-column: 2; /* Dritte Spalte */
  grid-row: 1; /* Erste und einzige Zeile */
}

#close-submenu-fab {
  background: var(--light-grey);
  color: var(--really-grey);
  grid-column: 4; /* Erste Spalte */
  grid-row: 1; /* Erste und einzige Zeile */
}

#pokedex-submenu-fab:active,
#encounters-submenu-fab:active,
#pokemon-caught-fab:active,
#close-submenu-fab:active {
  filter: brightness(0.92);
}

/* Animation für das Ein- und Ausblenden der FABs */
.fab-bar.submenu-active .fab-btn {
  opacity: 0;
  transform: scale(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: none; /* Disable interaction when hidden */
  visibility: hidden; /* Completely hide */
}

.fab-bar:not(.submenu-active) .fab-btn {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: auto; /* Enable interaction when visible */
  visibility: visible; /* Make sure they're visible */
}
