# TODOS - TrainerBattleScreen Redesign

## Projektübersicht
Umbau des TrainerBattleScreen.js um das visuelle Design und die Animationen des BattleScreen.js zu übernehmen, während die spezifische 6-gegen-6 Trainer-Kampf-Logik beibehalten wird.

## Prioritäten
- 🔴 **Hoch** - Kritische Funktionalität für den Grundbetrieb
- 🟡 **Mittel** - Wichtige Features für vollständige Funktionalität
- 🟢 **Niedrig** - Verbesserungen und Feinschliff

## 🐛 Bugfixes

### 🔴 Hoch
- [x] **Trainer DisplayName Anzeige korrigieren**
  - **Problem**: Trainer-<PERSON><PERSON><PERSON> zeigt "Pokemon_Breeder" statt "Pokemon Breeder"
  - **Lösung**: Unterstriche durch Leerzeichen ersetzen bei der Anzeige des displayName aus trainerTypes.json
  - **Dateien**: Trainer.js, TrainerBattleScreen.js oder entsprechende Tooltip-Komponente
  - **Akzeptanzkriterien**: DisplayName wird korrekt mit Leerzeichen angezeigt

## 🎨 UI/UX Redesign

### 🔴 Hoch
- [x] **CSS-Struktur von BattleScreen übernehmen**
  - **Beschreibung**: trainer-battle.css um die Struktur von battle-screen.css erweitern
  - **Akzeptanzkriterien**: Gleiche visuelle Struktur wie BattleScreen (Hintergrundbild, zwei Spalten, Positionierung)
  - **Abhängigkeiten**: Keine
  - **Dateien**: trainer-battle.css

- [x] **HTML-Template umstrukturieren**
  - **Beschreibung**: TrainerBattleScreen HTML-Template an BattleScreen-Struktur anpassen
  - **Details**:
    - Battle-arena mit background-image
    - Zwei Seiten (player-side, opponent-side) für Sprites
    - Info-Container für Pokemon-Karten oben
    - Battle-results Bereich unten
  - **Akzeptanzkriterien**: HTML-Struktur entspricht BattleScreen.js
  - **Abhängigkeiten**: CSS-Struktur muss fertig sein

### 🔴 Hoch
- [x] **Pokemon-Lebenspunkte-System implementieren**
  - **Beschreibung**: HP-Balken für beide Trainer-Pokemon wie in BattleScreen
  - **Details**:
    - Pokemon-Karten mit Namen, Level, HP-Balken, XP-Leiste (nur Spieler)
    - Typ-Badges mit Effektivitäts-Anzeigen
    - Positionierung identisch zu BattleScreen
  - **Akzeptanzkriterien**: HP-Balken funktionieren wie in BattleScreen
  - **Abhängigkeiten**: HTML-Template muss umstrukturiert sein

- [x] **6-Pokemon Indikator-System**
  - **Beschreibung**: 6 rote Bälle pro Seite zur Anzeige verfügbarer Pokemon
  - **Details**:
    - Rote Bälle für aktive Pokemon
    - Graue Bälle für besiegte Pokemon
    - Positionierung links und rechts der Battle-Arena
  - **CSS-Klassen**: `.pokemon-indicator`, `.pokemon-indicator.defeated`
  - **Akzeptanzkriterien**: 6 Bälle pro Seite, Farbwechsel bei Niederlage
  - **Abhängigkeiten**: HTML-Template

## 🎬 Animationen & Übergänge

### 🔴 Hoch
- [x] **Trainer-Intro Animation**
  - **Beschreibung**: 3-Sekunden Intro mit Trainer-Sprites
  - **Details**:
    - Spieler-Sprite: `./src/PlayerSprites/playerTrainer.png`
    - Gegner-Sprite: aus Trainer-Objekt (getBattleSpritePath())
    - Namen anzeigen statt Pokemon-Namen
    - Nach 3 Sekunden: slide-left + fade-out (Spieler), slide-right + fade-out (Gegner)
  - **Akzeptanzkriterien**: Smooth 3s Intro, dann Übergang zu Pokemon
  - **Abhängigkeiten**: CSS-Struktur, HTML-Template

### 🟡 Mittel
- [ ] **Pokemon Wechsel-Animationen**
  - **Beschreibung**: Fade-out für besiegtes Pokemon, fade-in für neues Pokemon
  - **Details**:
    - Besiegtes Pokemon: Schüttel-Animation + fade-out
    - Neues Pokemon: fade-in Animation
    - Gewinner-Pokemon bleibt mit 90% HP
  - **Akzeptanzkriterien**: Smooth Übergänge zwischen Pokemon-Wechseln
  - **Abhängigkeiten**: HP-System, Pokemon-Indikator-System

- [ ] **HP-Balken Animationen übernehmen**
  - **Beschreibung**: HP-Balken Animationen von BattleScreen.js adaptieren
  - **Details**: Gleiche Animation-Timings und -Effekte wie BattleScreen
  - **Akzeptanzkriterien**: HP-Balken animieren identisch zu BattleScreen
  - **Abhängigkeiten**: Pokemon-Lebenspunkte-System

## ⚔️ Kampf-Logik

### 🔴 Hoch
- [x] **Rundenbasiertes Kampfsystem**
  - **Beschreibung**: 1-gegen-1 Runden innerhalb des 6-gegen-6 Kampfes
  - **Details**:
    - Kampfformel von BattleScreen.js verwenden
    - Rundenweise Auswertung
    - Gewinner behält 90% HP für nächste Runde
    - Verlierer wechselt Pokemon
  - **Akzeptanzkriterien**: Jede Runde wird korrekt ausgewertet
  - **Abhängigkeiten**: Animationen müssen implementiert sein

- [x] **XP-System für Trainer-Kämpfe**
  - **Beschreibung**: XP-Vergabe nach gewonnenen Runden
  - **Details**:
    - XP nur bei Rundensieg
    - Kein XP bei Rundenniederlage
    - Level-up Animationen wie in BattleScreen
    - XP-Balken Updates nach jeder gewonnenen Runde
  - **Akzeptanzkriterien**: XP wird korrekt vergeben und animiert
  - **Abhängigkeiten**: Rundenbasiertes Kampfsystem

### 🟡 Mittel
- [x] **Kampfverlauf-Protokoll erweitern**
  - **Beschreibung**: Detailliertes Log aller Runden
  - **Details**:
    - Rundennummer
    - Beteiligte Pokemon
    - Schadenswerte
    - Rundensieger
    - Endergebnis
  - **Akzeptanzkriterien**: Vollständiger Kampfverlauf sichtbar
  - **Abhängigkeiten**: Rundenbasiertes Kampfsystem

## 🔧 Code-Refactoring

### 🟡 Mittel
- [x] **BattleSession für Trainer-Kämpfe anpassen**
  - **Beschreibung**: BattleSession.js erweitern für rundenweise Kämpfe
  - **Details**:
    - Methoden für Pokemon-Wechsel
    - Rundenverwaltung
    - HP-Persistierung zwischen Runden
  - **Akzeptanzkriterien**: BattleSession unterstützt Trainer-Kampf-Logik
  - **Abhängigkeiten**: Kampf-Logik muss definiert sein

- [ ] **Gemeinsame Utility-Funktionen extrahieren**
  - **Beschreibung**: Geteilte Funktionen zwischen BattleScreen und TrainerBattleScreen
  - **Details**:
    - HP-Balken Rendering
    - Pokemon-Karten Rendering
    - Animations-Utilities
    - Type-Effectiveness Display
  - **Akzeptanzkriterien**: Keine Code-Duplikation zwischen den Screens
  - **Abhängigkeiten**: Beide Screens müssen funktionsfähig sein

## 🎯 Finale Integration

### 🟢 Niedrig
- [ ] **Responsive Design optimieren**
  - **Beschreibung**: TrainerBattleScreen für verschiedene Bildschirmgrößen
  - **Details**: Media Queries von BattleScreen übernehmen und anpassen
  - **Akzeptanzkriterien**: Funktioniert auf allen Geräten
  - **Abhängigkeiten**: Alle anderen Features müssen implementiert sein

- [ ] **Performance-Optimierungen**
  - **Beschreibung**: Animationen und Rendering optimieren
  - **Details**:
    - Effiziente DOM-Updates
    - Smooth Animationen auch auf schwächeren Geräten
  - **Akzeptanzkriterien**: Keine Performance-Probleme
  - **Abhängigkeiten**: Vollständige Implementierung

## Technische Spezifikationen
- **Plattform**: Web (JavaScript)
- **Framework**: Vanilla JavaScript
- **Styling**: CSS3 mit Animationen
- **Abhängigkeiten**: Bestehende BattleScreen.js Logik
- **Kompatibilität**: Muss mit bestehendem Pokemon-Manager und GameState funktionieren

## Grundsätzliche Aufgaben
- [ ] **Analyse der bestehenden Implementierung**: Verstehen der BattleScreen und TrainerBattleScreen Strukturen
- [ ] **Dokumentation der Änderungen**: Jeder Änderung muss ein Eintrag in dieser Datei folgen
- [ ] **XP-Vergabe mit storage-service.js synchronisieren**: XP-Updates müssen in der Datenbank persistiert bleiben für das jeweilige Pokemon-Objekt

## Ressourcen
- BattleScreen.js als Referenz-Implementation
- battle-screen.css für Styling-Referenz
- TrainerBattleScreen.js als Basis-Implementation
- trainerTypes.json für Trainer-Daten
- pokemon-types-battle.json für Type-Effectiveness
