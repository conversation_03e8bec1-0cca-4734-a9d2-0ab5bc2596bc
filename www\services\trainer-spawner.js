// services/trainer-spawner.js
// Service for spawning trainers in the game world

import { config } from '../config.js';
import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import Trainer from '../Trainer.js';
import Pokemon from '../Pokemon.js';
import { distanceMeters } from './pokemon-spawner.js';
import turf from '../lib/turf.js';
import { findPokemonInPokedex } from '../utils/pokemon-utils.js';

export class TrainerSpawner {
    constructor() {
        this.trainerTypes = null;
        this.npcNames = null;
        this.spawnRadius = 50; // 50 Meter um den Spieler
    }

    /**
     * Load trainer types and NPC names
     * @returns {Promise<void>}
     */
    async loadData() {
        try {
            // Load trainer types
            if (!this.trainerTypes) {
                const trainerTypesResponse = await fetch('./trainerTypes.json');
                const trainerTypesData = await trainerTypesResponse.json();
                this.trainerTypes = trainerTypesData.trainerTypes;
                logger.debug(`Loaded ${Object.keys(this.trainerTypes).length} trainer types`);
            }

            // Load NPC names
            if (!this.npcNames) {
                const npcNamesResponse = await fetch('./src/NPCNames/NPCNames.json');
                this.npcNames = await npcNamesResponse.json();
                logger.debug(`Loaded ${this.npcNames.male.length} male and ${this.npcNames.female.length} female NPC names`);
            }
        } catch (error) {
            logger.error('Error loading trainer data:', error);
            throw error;
        }
    }

    /**
     * Spawn a random trainer near the player
     * @param {number} playerLat - Player's latitude
     * @param {number} playerLng - Player's longitude
     * @returns {Promise<Trainer|null>} - The spawned trainer or null if failed
     */
    async spawnRandomTrainer(playerLat, playerLng) {
        try {
            // Ensure data is loaded
            await this.loadData();

            // Get random trainer type
            const trainerTypeKeys = Object.keys(this.trainerTypes);
            const randomTypeKey = trainerTypeKeys[Math.floor(Math.random() * trainerTypeKeys.length)];
            const trainerTypeData = this.trainerTypes[randomTypeKey];

            // Get random variant
            const variants = trainerTypeData.sprites.variants;
            const randomVariant = variants[Math.floor(Math.random() * variants.length)];

            // Get appropriate name based on gender
            const names = this.npcNames[randomVariant.gender];
            const randomName = names[Math.floor(Math.random() * names.length)];

            // Generate team based on preferences
            const team = await this.generateTeam(trainerTypeData.preferences, trainerTypeData.levelRange);

            // Create trainer
            const trainer = new Trainer(
                randomTypeKey,
                randomVariant,
                randomName,
                team,
                trainerTypeData.levelRange
            );

            // Set random position within spawn radius
            const position = this.generateRandomPosition(playerLat, playerLng);
            trainer.setPosition(position.lat, position.lng);

            logger.debug(`Spawned trainer: ${trainer.name} (${trainer.trainerType}) at ${position.lat}, ${position.lng}`);
            return trainer;

        } catch (error) {
            logger.error('Error spawning random trainer:', error);
            return null;
        }
    }

    /**
     * Generate a random position within the spawn radius
     * @param {number} centerLat - Center latitude
     * @param {number} centerLng - Center longitude
     * @returns {Object} - {lat, lng} position
     */
    generateRandomPosition(centerLat, centerLng) {
        try {
            // Use turf.js for accurate positioning
            const randomAngle = Math.random() * 360;
            const distance = Math.random() * this.spawnRadius;

            const dest = turf.destination(
                [centerLng, centerLat],
                distance / 1000, // turf uses km
                randomAngle
            );

            return {
                lat: dest.geometry.coordinates[1],
                lng: dest.geometry.coordinates[0]
            };
        } catch (error) {
            logger.error('Error using turf.js for trainer position:', error);
            // Fallback to simple offset calculation
            const latOffset = (Math.random() - 0.5) * (this.spawnRadius / 111320);
            const lngOffset = (Math.random() - 0.5) * (this.spawnRadius / (111320 * Math.cos(centerLat * Math.PI / 180)));

            return {
                lat: centerLat + latOffset,
                lng: centerLng + lngOffset
            };
        }
    }

    /**
     * Generate a team based on trainer preferences
     * @param {Array} preferences - Array of preferred Pokemon types
     * @param {Object} levelRange - Level range object with min/max
     * @returns {Promise<Array>} - Array of Pokemon
     */
    async generateTeam(preferences, levelRange) {
        const team = [];
        const teamSize = 6; // Always generate 6 Pokemon

        try {
            // Ensure we have pokedex data
            if (!gameState.pokedexData || gameState.pokedexData.length === 0) {
                logger.warn('No pokedex data available for team generation');
                return team;
            }

            for (let i = 0; i < teamSize; i++) {
                const pokemon = await this.generatePokemonForTeam(preferences, levelRange);
                if (pokemon) {
                    team.push(pokemon);
                }
            }

            logger.debug(`Generated team of ${team.length} Pokemon for trainer`);
            return team;

        } catch (error) {
            logger.error('Error generating trainer team:', error);
            return team;
        }
    }

    /**
     * Generate a single Pokemon for the team
     * @param {Array} preferences - Array of preferred Pokemon types
     * @param {Object} levelRange - Level range object with min/max
     * @returns {Promise<Pokemon|null>} - Generated Pokemon or null
     */
    async generatePokemonForTeam(preferences, levelRange) {
        try {
            // Find Pokemon that match the trainer's type preferences
            const matchingPokemon = gameState.pokedexData.filter(pokeEntry => {
                if (!pokeEntry.types || !Array.isArray(pokeEntry.types)) {
                    return false;
                }

                // Check if any of the Pokemon's types match the trainer's preferences
                return pokeEntry.types.some(pokemonType => {
                    return preferences.some(preferredType => {
                        // Case-insensitive comparison
                        return pokemonType.toLowerCase() === preferredType.toLowerCase();
                    });
                });
            });

            if (matchingPokemon.length === 0) {
                logger.warn('No Pokemon found matching trainer preferences:', preferences);
                // Fallback to any Pokemon
                const randomEntry = gameState.pokedexData[Math.floor(Math.random() * gameState.pokedexData.length)];
                return await this.createPokemonFromEntry(randomEntry, levelRange);
            }

            // Select random Pokemon from matching ones
            const randomEntry = matchingPokemon[Math.floor(Math.random() * matchingPokemon.length)];
            return await this.createPokemonFromEntry(randomEntry, levelRange);

        } catch (error) {
            logger.error('Error generating Pokemon for team:', error);
            return null;
        }
    }

    /**
     * Create a Pokemon from a pokedex entry with proper evolution
     * @param {Object} pokeEntry - Pokedex entry
     * @param {Object} levelRange - Level range object with min/max
     * @returns {Promise<Pokemon|null>} - Created Pokemon or null
     */
    async createPokemonFromEntry(pokeEntry, levelRange) {
        try {
            // Generate random level within range
            const level = Math.floor(Math.random() * (levelRange.max - levelRange.min + 1)) + levelRange.min;

            // Create pokedex snapshot to prevent race conditions (same as Pokemon spawner)
            const pokedexSnapshot = JSON.parse(JSON.stringify(gameState.pokedexData));

            // Calculate evolution data with isolated pokedex snapshot
            const evolutionData = await this.calculateEvolutionData(pokeEntry.name, level, pokedexSnapshot);

            // Create Pokemon with base data first
            const pokemon = new Pokemon(
                pokeEntry.name, // Base name
                pokeEntry.types[0], // Primary type
                level,
                {
                    types: pokeEntry.types,
                    evolution_level: pokeEntry.evolution_level,
                    evolution_item: pokeEntry.evolution_item,
                    dex: pokeEntry.dex_number,
                    image_url: pokeEntry.image_url,
                    rarity: pokeEntry.rarity,
                    evolution_chain_id: pokeEntry.evolution_chain_id
                }
            );

            // Apply evolution data (same pattern as Pokemon spawner)
            pokemon.base_name = pokeEntry.name; // English base name
            pokemon.base_sprite = pokeEntry.image_url;
            pokemon.rarity = pokeEntry.rarity || 'common';

            // Set the evolved form data (keeping English names for internal logic)
            pokemon.name = evolutionData.name; // English evolved name
            pokemon.image_url = evolutionData.sprite;
            pokemon.image = evolutionData.sprite; // Ensure image is set
            pokemon.dex_number = evolutionData.dex_number;
            pokemon.types = evolutionData.types;
            pokemon.evolution_chain_id = evolutionData.evolution_chain_id;

            logger.debug(`Created trainer Pokemon: ${pokemon.name} (Level ${level}) evolved from ${pokeEntry.name}`);
            return pokemon;

        } catch (error) {
            logger.error('Error creating Pokemon from entry:', error);
            return null;
        }
    }

    /**
     * Calculate evolution data with isolated pokedex (same as Pokemon spawner)
     * @param {string} baseName - Base Pokemon name
     * @param {number} level - Pokemon level
     * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
     * @returns {Promise<Object>} - Evolution data {name, sprite, dex_number, types}
     */
    async calculateEvolutionData(baseName, level, pokedexSnapshot) {
        // Validate input parameters
        if (!baseName || typeof baseName !== 'string') {
            throw new Error(`Invalid baseName for evolution calculation: ${baseName}`);
        }
        if (!level || typeof level !== 'number' || level < 1 || level > 100) {
            throw new Error(`Invalid level for evolution calculation: ${level}`);
        }
        if (!pokedexSnapshot || !Array.isArray(pokedexSnapshot) || pokedexSnapshot.length === 0) {
            throw new Error(`Invalid pokedex snapshot for evolution calculation: ${pokedexSnapshot?.length || 'null'} entries`);
        }

        // Create temporary Pokemon object only for evolution calculation
        const tempPokemon = new Pokemon(baseName, 'normal', level);

        // Calculate evolution with isolated pokedex
        const displayForm = await tempPokemon.getDisplayForm(pokedexSnapshot);

        // Validate evolution result
        if (!displayForm || !displayForm.name || !displayForm.dex_number) {
            logger.error(`Invalid evolution result for ${baseName} level ${level}`);

            // Fallback to base Pokemon data
            const baseEntry = pokedexSnapshot.find(p =>
                p.name.toLowerCase() === baseName.toLowerCase()
            );

            if (baseEntry) {
                return {
                    name: baseEntry.name,
                    sprite: baseEntry.image_url,
                    dex_number: baseEntry.dex_number,
                    types: baseEntry.types,
                    evolution_chain_id: baseEntry.evolution_chain_id
                };
            } else {
                // Ultimate fallback
                return {
                    name: baseName,
                    sprite: '',
                    dex_number: null,
                    types: ['normal'],
                    evolution_chain_id: null
                };
            }
        }

        return {
            name: displayForm.name,
            sprite: displayForm.sprite,
            dex_number: displayForm.dex_number,
            types: displayForm.types,
            evolution_chain_id: displayForm.evolution_chain_id
        };
    }
}

// Export singleton instance
export const trainerSpawner = new TrainerSpawner();
