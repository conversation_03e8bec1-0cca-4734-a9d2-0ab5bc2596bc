// capacitor/app.js
// Wrapper für das Capacitor App-Plugin

import { logger } from '../utils/logger.js';

// Variable für den "Double Back to Exit"-Mechanismus
let lastBackPressTime = 0;
let exitToastShown = false;

// Globale Variable, um den aktuellen Zustand zu verfolgen
let isOverlayOpen = false;
let mainMapBackButtonHandler = null;

/**
 * Registriert einen Event-Listener für den Hardware-Zurück-Button in Overlay-Screens
 * @param {Function} callback - Die Funktion, die aufgerufen wird, wenn der Zurück-Button gedrückt wird
 * @returns {Function} - Eine Funktion zum Entfernen des Event-Listeners
 */
export function registerBackButtonHandler(callback) {
  // Markieren, dass ein Overlay geöffnet ist
  isOverlayOpen = true;

  // Zurücksetzen der Double-Back-Variablen
  lastBackPressTime = 0;
  exitToastShown = false;

  // Wenn der Hauptscreen-Handler aktiv ist, deaktivieren wir ihn
  if (mainMapBackButtonHandler) {
    try {
      mainMapBackButtonHandler();
      mainMapBackButtonHandler = null;
    } catch (e) {
      logger.error('Error removing main map back button handler:', e);
    }
  }

  // Prüfen, ob wir auf einem Gerät mit Hardware-Zurück-Button sind
  const isCapacitorApp = window.Capacitor && window.Capacitor.isNativePlatform();

  if (!isCapacitorApp) {
    // Wenn wir nicht auf einem Gerät sind, nichts tun
    return () => {
      isOverlayOpen = false;
      // Hauptscreen-Handler wieder aktivieren
      setupMainMapBackButtonHandler();
    };
  }

  // Event-Listener-Funktion
  const handleBackButton = () => {
    // Callback aufrufen
    callback();

    // Event verhindern (verhindert, dass die App minimiert wird)
    return false;
  };

  // Event-Listener registrieren
  // Wir verwenden das globale Capacitor-Objekt, das automatisch verfügbar ist
  try {
    // Sicherstellen, dass alle Objekte existieren, bevor wir darauf zugreifen
    if (window.Capacitor && window.Capacitor.Plugins && window.Capacitor.Plugins.App) {
      const appPlugin = window.Capacitor.Plugins.App;

      // Protokollieren, dass wir den Event-Listener registrieren
      logger.debug('Registering back button handler for overlay');

      // Alle vorherigen Listener entfernen, um Konflikte zu vermeiden
      try {
        appPlugin.removeAllListeners();
      } catch (e) {
        logger.debug('Could not remove previous listeners, may not exist:', e.message);
      }

      // Neuen Listener hinzufügen
      appPlugin.addListener('backButton', handleBackButton);

      // Funktion zum Entfernen des Event-Listeners zurückgeben
      return () => {
        try {
          logger.debug('Removing back button handler for overlay');
          appPlugin.removeAllListeners();

          // Markieren, dass das Overlay geschlossen ist
          isOverlayOpen = false;

          // Hauptscreen-Handler wieder aktivieren
          setupMainMapBackButtonHandler();
        } catch (e) {
          logger.error('Error removing back button listener:', e);
        }
      };
    } else {
      logger.warn('Capacitor App plugin not available, back button handler not registered');
    }
  } catch (e) {
    logger.error('Error adding back button listener:', e);
  }

  // Fallback, wenn etwas schief geht
  return () => {
    isOverlayOpen = false;
    // Hauptscreen-Handler wieder aktivieren
    setupMainMapBackButtonHandler();
  };
}

/**
 * Hilfsfunktion zum Einrichten des Hauptscreen-Handlers
 * Diese Funktion wird aufgerufen, wenn ein Overlay geschlossen wird
 */
function setupMainMapBackButtonHandler() {
  // Nur einrichten, wenn kein Overlay geöffnet ist
  if (!isOverlayOpen) {
    // Zurücksetzen der Double-Back-Variablen
    lastBackPressTime = 0;
    exitToastShown = false;

    // Hauptscreen-Handler einrichten
    mainMapBackButtonHandler = registerMainMapBackButtonHandlerInternal();
  }
}

/**
 * Interne Funktion zum Registrieren des Event-Listeners für den Hardware-Zurück-Button auf der Hauptkarte
 * @returns {Function} - Eine Funktion zum Entfernen des Event-Listeners
 * @private
 */
function registerMainMapBackButtonHandlerInternal() {
  // Prüfen, ob wir auf einem Gerät mit Hardware-Zurück-Button sind
  const isCapacitorApp = window.Capacitor && window.Capacitor.isNativePlatform();

  if (!isCapacitorApp) {
    // Wenn wir nicht auf einem Gerät sind, nichts tun
    return () => {};
  }

  // Event-Listener-Funktion für "Double Back to Exit"
  const handleDoubleBackToExit = () => {
    // Wenn ein Overlay geöffnet ist, nichts tun
    if (isOverlayOpen) {
      return false;
    }

    const currentTime = new Date().getTime();

    // Wenn der letzte Zurück-Button-Druck weniger als 2 Sekunden her ist
    if (currentTime - lastBackPressTime < 2000) {
      // App minimieren/beenden
      try {
        if (window.Capacitor && window.Capacitor.Plugins && window.Capacitor.Plugins.App) {
          window.Capacitor.Plugins.App.exitApp();
        }
      } catch (e) {
        logger.error('Error exiting app:', e);
      }
      return true;
    } else {
      // Ersten Zurück-Button-Druck speichern
      lastBackPressTime = currentTime;

      // Toast-Nachricht anzeigen (nur einmal)
      if (!exitToastShown) {
        // Einfache Toast-Nachricht erstellen
        const toast = document.createElement('div');
        toast.textContent = 'Drücke erneut zum Beenden';
        toast.style.position = 'fixed';
        toast.style.bottom = '80px';
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        toast.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        toast.style.color = 'white';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '20px';
        toast.style.zIndex = '10000';
        document.body.appendChild(toast);

        // Toast nach 2 Sekunden ausblenden
        setTimeout(() => {
          document.body.removeChild(toast);
          exitToastShown = false;
        }, 2000);

        exitToastShown = true;
      }

      // Event verhindern (App bleibt geöffnet)
      return false;
    }
  };

  // Event-Listener registrieren
  try {
    if (window.Capacitor && window.Capacitor.Plugins && window.Capacitor.Plugins.App) {
      const appPlugin = window.Capacitor.Plugins.App;

      // Protokollieren, dass wir den Event-Listener registrieren
      logger.debug('Registering main map back button handler');

      // Alle vorherigen Listener entfernen, um Konflikte zu vermeiden
      try {
        appPlugin.removeAllListeners();
      } catch (e) {
        logger.debug('Could not remove previous listeners, may not exist:', e.message);
      }

      // Neuen Listener hinzufügen
      appPlugin.addListener('backButton', handleDoubleBackToExit);

      // Funktion zum Entfernen des Event-Listeners zurückgeben
      return () => {
        try {
          logger.debug('Removing main map back button handler');
          appPlugin.removeAllListeners();
        } catch (e) {
          logger.error('Error removing back button listener:', e);
        }
      };
    } else {
      logger.warn('Capacitor App plugin not available, main map back button handler not registered');
    }
  } catch (e) {
    logger.error('Error adding back button listener:', e);
  }

  // Fallback, wenn etwas schief geht
  return () => {};
}

/**
 * Öffentliche Funktion zum Registrieren des Event-Listeners für den Hardware-Zurück-Button auf der Hauptkarte
 * Implementiert "Double Back to Exit"-Verhalten
 */
export function registerMainMapBackButtonHandler() {
  // Nur einrichten, wenn kein Overlay geöffnet ist
  if (!isOverlayOpen) {
    mainMapBackButtonHandler = registerMainMapBackButtonHandlerInternal();
  }
}

/**
 * Prüft, ob die App auf einem nativen Gerät läuft
 * @returns {boolean} - true, wenn die App auf einem nativen Gerät läuft
 */
export function isNativePlatform() {
  return window.Capacitor && window.Capacitor.isNativePlatform();
}
