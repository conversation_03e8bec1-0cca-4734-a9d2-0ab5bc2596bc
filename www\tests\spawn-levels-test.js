// tests/spawn-levels-test.js
// Test script for spawn-levels.js and evolution functionality

import { logger } from '../utils/logger.js';
import { getSpawnLevel } from '../services/spawn-levels.js';
import { pokemonManager } from '../services/pokemon-manager.js';
import Pokemon from '../Pokemon.js';
import { gameState } from '../state/game-state.js';

// Test configuration
const TEST_ITERATIONS = 20;
const TEST_POKEMON_FAMILIES = [
  // Format: [base_name, evolution_level, evolved_name]
  ['bulbasaur', 16, 'ivysaur'],
  ['charmander', 16, 'charmeleon'],
  ['squirtle', 16, 'wartortle'],
  ['caterpie', 7, 'metapod'],
  ['weedle', 7, 'kakuna'],
  ['pidgey', 18, 'pidgeotto'],
  ['rattata', 20, 'raticate'],
  ['pikachu', 25, 'raichu'],
  ['sandshrew', 22, 'sandslash'],
  ['nidoran-f', 16, 'nidorina'],
  ['oddish', 21, 'gloom'],
  ['diglett', 26, 'dugtrio'],
  ['meowth', 28, 'persian'],
  ['psyduck', 33, 'golduck'],
  ['mankey', 28, 'primeape'],
  ['poliwag', 25, 'poliwhirl'],
  ['abra', 16, 'kadabra'],
  ['machop', 28, 'machoke'],
  ['geodude', 25, 'graveler'],
  ['magnemite', 30, 'magneton']
];

/**
 * Initialize the test environment
 */
async function initializeTest() {
  try {
    // Load pokedex data
    const pokedexResp = await fetch('../pokedex-151.json');
    gameState.pokedexData = await pokedexResp.json();
    
    // Debug: Log the first few Pokemon in the pokedex
    logger.info(`Loaded ${gameState.pokedexData.length} Pokemon in pokedex`);
    logger.info(`First Pokemon: ${JSON.stringify(gameState.pokedexData[0])}`);
    
    // Debug: Check if we can find some test Pokemon
    const testPokemon = ['bulbasaur', 'charmander', 'squirtle', 'pikachu'];
    for (const name of testPokemon) {
      const found = gameState.pokedexData.find(p => p.name === name);
      logger.info(`Search for "${name}": ${found ? 'FOUND' : 'NOT FOUND'}`);
      if (found) {
        logger.info(`Details: ${JSON.stringify(found)}`);
      }
    }

    // Initialize Pokemon manager
    await pokemonManager.initialize();
    
    // Create a test team with different levels
    await createTestTeam();
    
    // Run the tests
    await runSpawnLevelTest();
    await runEvolutionTest();
    
    logger.info('All tests completed!');
  } catch (error) {
    logger.error('Test initialization error:', error);
  }
}

/**
 * Create a test team with Pokemon at different levels
 */
async function createTestTeam() {
  try {
    // Clear existing team
    const existingTeam = pokemonManager.getTeamPokemon();
    for (const pokemon of existingTeam) {
      await pokemonManager.removeFromTeam(pokemon.id);
    }
    
    // Create test team with 3 Pokemon at different levels
    const teamData = [
      { name: 'pikachu', type: 'electric', level: 15, rarity: 'starter' },
      { name: 'charmander', type: 'fire', level: 10, rarity: 'starter' },
      { name: 'squirtle', type: 'water', level: 5, rarity: 'starter' }
    ];
    
    for (const data of teamData) {
      const pokemon = new Pokemon(data.name, data.type, data.level);
      pokemon.rarity = data.rarity;
      
      // Add to Pokemon manager
      await pokemonManager.addPokemon(pokemon);
      await pokemonManager.addToTeam(pokemon.id);
    }
    
    const team = pokemonManager.getTeamPokemon();
    logger.info(`Created test team with ${team.length} Pokemon:`, team.map(p => `${p.name} (Lvl ${p.level})`).join(', '));
  } catch (error) {
    logger.error('Error creating test team:', error);
  }
}

/**
 * Test the spawn level distribution
 */
async function runSpawnLevelTest() {
  logger.info('=== SPAWN LEVEL TEST ===');
  
  const levels = [];
  const team = pokemonManager.getTeamPokemon();
  const avgTeamLevel = Math.round(team.reduce((sum, p) => sum + p.level, 0) / team.length);
  
  logger.info(`Team average level: ${avgTeamLevel}`);
  
  // Generate multiple spawn levels
  for (let i = 0; i < TEST_ITERATIONS; i++) {
    const level = await getSpawnLevel();
    levels.push(level);
    logger.info(`Spawn #${i+1}: Level ${level} (${level === avgTeamLevel ? 'EXACT MATCH' : level < avgTeamLevel ? 'BELOW AVG' : 'ABOVE AVG'})`);
  }
  
  // Calculate statistics
  const exactMatches = levels.filter(l => l === avgTeamLevel).length;
  const belowAvg = levels.filter(l => l < avgTeamLevel).length;
  const aboveAvg = levels.filter(l => l > avgTeamLevel).length;
  
  logger.info('=== SPAWN LEVEL STATISTICS ===');
  logger.info(`Total spawns: ${levels.length}`);
  logger.info(`Exact matches: ${exactMatches} (${Math.round(exactMatches/levels.length*100)}%)`);
  logger.info(`Below average: ${belowAvg} (${Math.round(belowAvg/levels.length*100)}%)`);
  logger.info(`Above average: ${aboveAvg} (${Math.round(aboveAvg/levels.length*100)}%)`);
}

/**
 * Test the evolution functionality with different spawn levels
 */
async function runEvolutionTest() {
  logger.info('=== EVOLUTION TEST ===');
  
  // Find Pokemon with evolution data in the pokedex
  const pokemonWithEvolution = gameState.pokedexData.filter(p => p.evolution_level && p.evolution_chain_id);
  
  if (pokemonWithEvolution.length === 0) {
    logger.warn('No Pokemon with evolution data found in pokedex');
    return;
  }
  
  logger.info(`Found ${pokemonWithEvolution.length} Pokemon with evolution data`);
  
  // Test a few Pokemon with evolution data
  const testCount = Math.min(5, pokemonWithEvolution.length);
  
  for (let i = 0; i < testCount; i++) {
    const baseEntry = pokemonWithEvolution[i];
    const evoLevel = baseEntry.evolution_level;
    
    // Find the evolved form
    const evolvedEntry = gameState.pokedexData.find(p => 
      p.evolution_chain_id === baseEntry.evolution_chain_id && 
      p.dex_number !== baseEntry.dex_number
    );
    
    if (!evolvedEntry) {
      logger.warn(`Could not find evolved form for ${baseEntry.name} (${baseEntry.de})`);
      continue;
    }
    
    // Test with level below evolution threshold
    const belowThreshold = evoLevel - 1;
    const pokemonBelow = new Pokemon(baseEntry.name, baseEntry.types[0], belowThreshold);
    const displayFormBelow = pokemonBelow.getDisplayForm(gameState.pokedexData);
    
    // Test with level at evolution threshold
    const atThreshold = evoLevel;
    const pokemonAt = new Pokemon(baseEntry.name, baseEntry.types[0], atThreshold);
    const displayFormAt = pokemonAt.getDisplayForm(gameState.pokedexData);
    
    // Test with level above evolution threshold
    const aboveThreshold = evoLevel + 5;
    const pokemonAbove = new Pokemon(baseEntry.name, baseEntry.types[0], aboveThreshold);
    const displayFormAbove = pokemonAbove.getDisplayForm(gameState.pokedexData);
    
    // Get German names for display
    const baseNameDE = baseEntry.de || baseEntry.name;
    const evolvedNameDE = evolvedEntry.de || evolvedEntry.name;
    
    logger.info(`Testing evolution for ${baseNameDE} (evolves at level ${evoLevel} to ${evolvedNameDE}):`);
    
    // Compare the actual name or the German name
    const isBaseForm = (form) => 
      form.name === baseEntry.name || 
      form.name === baseEntry.de;
      
    const isEvolvedForm = (form) => 
      form.name === evolvedEntry.name || 
      form.name === evolvedEntry.de;
    
    logger.info(`- Level ${belowThreshold}: ${displayFormBelow.name} (${isBaseForm(displayFormBelow) ? 'CORRECT' : 'ERROR'})`);
    logger.info(`- Level ${atThreshold}: ${displayFormAt.name} (${isEvolvedForm(displayFormAt) ? 'CORRECT' : 'ERROR'})`);
    logger.info(`- Level ${aboveThreshold}: ${displayFormAbove.name} (${isEvolvedForm(displayFormAbove) ? 'CORRECT' : 'ERROR'})`);
  }
}

// Export the test function
export function runTests() {
  logger.info('Starting spawn level and evolution tests...');
  initializeTest();
}
