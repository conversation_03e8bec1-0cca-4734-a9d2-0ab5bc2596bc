// ui/Component.js
// Base component class for UI elements

import { logger } from '../utils/logger.js';

export class Component {
  /**
   * Create a new component
   * @param {HTMLElement} container - The container element
   * @param {Object} options - Component options
   */
  constructor(container, options = {}) {
    this.container = container;
    this.options = options;
    this.elements = {};
    this.eventListeners = [];
    this.isRendered = false;
  }
  
  /**
   * Render the component
   * @returns {HTMLElement} - The rendered container
   */
  render() {
    // To be implemented by subclasses
    this.isRendered = true;
    return this.container;
  }
  
  /**
   * Add event listeners
   */
  addEventListeners() {
    // To be implemented by subclasses
  }
  
  /**
   * Remove event listeners
   */
  removeEventListeners() {
    this.eventListeners.forEach(({ element, event, listener }) => {
      element.removeEventListener(event, listener);
    });
    this.eventListeners = [];
  }
  
  /**
   * Add an event listener and track it for later removal
   * @param {HTMLElement} element - The element to add the listener to
   * @param {string} event - The event name
   * @param {Function} listener - The event listener
   */
  addEventListener(element, event, listener) {
    element.addEventListener(event, listener);
    this.eventListeners.push({ element, event, listener });
  }
  
  /**
   * Show the component
   */
  show() {
    if (!this.isRendered) {
      this.render();
      this.addEventListeners();
    }
    this.container.style.display = 'block';
  }
  
  /**
   * Hide the component
   */
  hide() {
    this.container.style.display = 'none';
  }
  
  /**
   * Destroy the component
   */
  destroy() {
    this.removeEventListeners();
    this.container.innerHTML = '';
    this.isRendered = false;
  }
  
  /**
   * Create an element with attributes and children
   * @param {string} tag - The tag name
   * @param {Object} attributes - The element attributes
   * @param {Array|string} children - The element children
   * @returns {HTMLElement} - The created element
   */
  createElement(tag, attributes = {}, children = []) {
    const element = document.createElement(tag);
    
    // Set attributes
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'style' && typeof value === 'object') {
        Object.entries(value).forEach(([styleKey, styleValue]) => {
          element.style[styleKey] = styleValue;
        });
      } else if (key === 'className') {
        element.className = value;
      } else if (key === 'dataset') {
        Object.entries(value).forEach(([dataKey, dataValue]) => {
          element.dataset[dataKey] = dataValue;
        });
      } else if (key.startsWith('on') && typeof value === 'function') {
        const eventName = key.slice(2).toLowerCase();
        this.addEventListener(element, eventName, value);
      } else {
        element.setAttribute(key, value);
      }
    });
    
    // Add children
    if (typeof children === 'string') {
      element.textContent = children;
    } else if (Array.isArray(children)) {
      children.forEach(child => {
        if (typeof child === 'string') {
          element.appendChild(document.createTextNode(child));
        } else if (child instanceof HTMLElement) {
          element.appendChild(child);
        }
      });
    }
    
    return element;
  }
  
  /**
   * Create an element and store it in the elements object
   * @param {string} id - The element ID
   * @param {string} tag - The tag name
   * @param {Object} attributes - The element attributes
   * @param {Array|string} children - The element children
   * @returns {HTMLElement} - The created element
   */
  createAndStoreElement(id, tag, attributes = {}, children = []) {
    const element = this.createElement(tag, attributes, children);
    this.elements[id] = element;
    return element;
  }
  
  /**
   * Set the HTML content of the container
   * @param {string} html - The HTML content
   */
  setHTML(html) {
    this.container.innerHTML = html;
  }
}
