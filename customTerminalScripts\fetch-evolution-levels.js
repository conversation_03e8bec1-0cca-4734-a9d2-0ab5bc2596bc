import fs from "fs/promises";
import fetch from "node-fetch";

async function getEvolutionData() {
  const evolutionMap = [];

  for (let dexId = 1; dexId <= 151; dexId++) {
    try {
      const speciesResponse = await fetch(`https://pokeapi.co/api/v2/pokemon-species/${dexId}/`);
      const speciesData = await speciesResponse.json();

      const evolutionChainUrl = speciesData.evolution_chain.url;
      const chainResponse = await fetch(evolutionChainUrl);
      const chainData = await chainResponse.json();

      // Finde die Basisform und deren Entwicklung
      let currentStage = chainData.chain;
      let evolutionLevel = null;
      let evolutionItem = null;

      // Suche die aktuelle Basisform in der Kette
      while (currentStage && currentStage.species.name !== speciesData.name) {
        if (currentStage.evolves_to.length > 0) {
          currentStage = currentStage.evolves_to[0];
        } else {
          currentStage = null;
        }
      }

      // Prüfe, ob es eine Entwicklung gibt
      if (currentStage && currentStage.evolves_to.length > 0) {
        const evolutionDetails = currentStage.evolves_to[0].evolution_details[0];
        if (evolutionDetails) {
          if (
            evolutionDetails.trigger?.name === "level-up" &&
            evolutionDetails.min_level
          ) {
            evolutionLevel = evolutionDetails.min_level;
          }
          if (
            evolutionDetails.trigger?.name === "use-item" &&
            evolutionDetails.item
          ) {
            evolutionItem = evolutionDetails.item.name;
          }
        }
      }

      evolutionMap.push({
        dex: dexId,
        name: speciesData.name,
        evolution_level: evolutionLevel,
        evolution_item: evolutionItem,
      });

      console.log(`Processed #${dexId} ${speciesData.name}`);
    } catch (error) {
      console.error(`Error processing #${dexId}:`, error);
    }
  }

  await fs.writeFile("evolutions.json", JSON.stringify(evolutionMap, null, 2));
  console.log("Data saved to evolutions.json");
}

getEvolutionData();
