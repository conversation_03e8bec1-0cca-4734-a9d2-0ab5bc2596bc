// utils/pokemon-display-names.js
import { logger } from './logger.js';
import { gameState } from '../state/game-state.js';

/**
 * Get the German display name for a Pokemon
 * @param {Object} pokemon - Pokemon object with name or dex_number
 * @returns {string} - German name or fallback to English name
 */
export function getGermanPokemonName(pokemon) {
  if (!pokemon) return 'Unbekannt';
  
  // Try to find the Pokemon in pokedex data by name or dex_number
  const pokedexData = gameState.pokedexData;
  if (!pokedexData || !Array.isArray(pokedexData)) {
    logger.warn('Pokedex data not available for German name lookup');
    return pokemon.name || 'Unbekannt';
  }
  
  let pokedexEntry = null;
  
  // First try to find by dex_number (most reliable)
  if (pokemon.dex_number) {
    pokedexEntry = pokedexData.find(p => p.dex_number === pokemon.dex_number);
  }
  
  // If not found, try by English name
  if (!pokedexEntry && pokemon.name) {
    pokedexEntry = pokedexData.find(p => 
      p.name && p.name.toLowerCase() === pokemon.name.toLowerCase()
    );
  }
  
  // Return German name if found, otherwise fallback to English name
  if (pokedexEntry && pokedexEntry.de) {
    return pokedexEntry.de;
  }
  
  logger.debug(`German name not found for Pokemon: ${pokemon.name} (dex: ${pokemon.dex_number})`);
  return pokemon.name || 'Unbekannt';
}

/**
 * Get display name with proper capitalization
 * @param {string} name - Name to capitalize
 * @returns {string} - Properly capitalized name
 */
export function capitalizePokemonName(name) {
  if (!name || typeof name !== 'string') return 'Unbekannt';
  return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
}
