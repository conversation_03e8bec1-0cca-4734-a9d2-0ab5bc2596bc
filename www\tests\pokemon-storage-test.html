<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pokemon Storage Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        #log {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .info {
            color: #0066cc;
        }
        .debug {
            color: #666;
        }
        .error {
            color: #cc0000;
            font-weight: bold;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Pokemon Storage Test</h1>
    <p>This test verifies that Pokemon objects can be properly saved to and loaded from storage with all their properties.</p>
    
    <button id="runTest">Run Test</button>
    <button id="clearLog">Clear Log</button>
    
    <div id="log"></div>
    
    <script type="module">
        import { logger } from '../utils/logger.js';
        
        // Override logger to display in the UI
        const logElement = document.getElementById('log');
        
        // Store original methods
        const originalDebug = logger.debug;
        const originalInfo = logger.info;
        const originalError = logger.error;
        
        // Override methods to also log to UI
        logger.debug = function(...args) {
            originalDebug.apply(logger, args);
            logToUI('debug', args);
        };
        
        logger.info = function(...args) {
            originalInfo.apply(logger, args);
            logToUI('info', args);
        };
        
        logger.error = function(...args) {
            originalError.apply(logger, args);
            logToUI('error', args);
        };
        
        function logToUI(level, args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            
            const logLine = document.createElement('div');
            logLine.className = level;
            logLine.textContent = `[${level.toUpperCase()}] ${message}`;
            logElement.appendChild(logLine);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Button event handlers
        document.getElementById('runTest').addEventListener('click', async () => {
            logElement.innerHTML = '';
            logger.info('Loading test module...');
            
            try {
                const module = await import('./pokemon-storage-test.js');
                logger.info('Test completed');
            } catch (error) {
                logger.error('Error loading test module:', error);
            }
        });
        
        document.getElementById('clearLog').addEventListener('click', () => {
            logElement.innerHTML = '';
        });
        
        // Initial message
        logger.info('Pokemon Storage Test ready. Click "Run Test" to start.');
    </script>
</body>
</html>
