// tests/pokemon-storage-test.js
// Test script for Pokemon storage and recreation

import { logger } from '../utils/logger.js';
import { storageService } from '../storage/storage-service.js';
import Pokemon from '../Pokemon.js';
import { saveSpawnsToStorage, loadSpawnsFromStorage } from '../capacitor/time-events.js';
import { gameState } from '../state/game-state.js';
import { findPokemonInPokedex } from '../utils/pokemon-utils.js';

// Test storage key
const TEST_STORAGE_KEY = 'pokemon-storage-test';

// Pokedex data
let pokedexData = null;

/**
 * Run the Pokemon storage test
 */
async function runTest() {
  try {
    logger.info('Starting Pokemon storage test...');

    // Load pokedex data directly from JSON file
    await loadPokedexData();

    if (!pokedexData || pokedexData.length === 0) {
      logger.error('Failed to load pokedex data, test cannot continue');
      return;
    }

    logger.info(`Successfully loaded pokedex data with ${pokedexData.length} Pokemon`);

    // Create test Pokemon with proper data
    const testPokemons = [
      createTestPokemon('squirtle', 'water', 30),
      createTestPokemon('zubat', 'poison', 11),
      createTestPokemon('staryu', 'water', 7)
    ];

    // Log the created Pokemon details
    logger.info('Created test Pokemon details:');
    testPokemons.forEach((pokemon, index) => {
      logger.info(`Pokemon ${index + 1}: ${pokemon.name} (ID: ${pokemon.id})`);
      logger.info(`  dex_number: ${pokemon.dex_number}`);
      logger.info(`  types: ${pokemon.types.join(', ')}`);
      logger.info(`  level: ${pokemon.level}`);
      logger.info(`  experience: ${pokemon.experience}`);
      logger.info(`  image: ${pokemon.image || '(none)'}`);
    });

    // Save test Pokemon to storage
    await storageService.set(TEST_STORAGE_KEY, testPokemons);
    logger.info(`Saved ${testPokemons.length} test Pokemon to storage`);

    // Load test Pokemon from storage
    const loadedPokemons = await storageService.get(TEST_STORAGE_KEY, []);
    logger.info(`Loaded ${loadedPokemons.length} test Pokemon from storage`);

    // Recreate Pokemon objects from loaded data
    const recreatedPokemons = loadedPokemons.map(data => Pokemon.fromJSON(data));

    // Log recreated Pokemon details
    logger.info('Recreated Pokemon details:');
    recreatedPokemons.forEach((pokemon, index) => {
      logger.info(`Pokemon ${index + 1}: ${pokemon.name} (ID: ${pokemon.id})`);
      logger.info(`  dex_number: ${pokemon.dex_number}`);
      logger.info(`  types: ${pokemon.types.join(', ')}`);
      logger.info(`  level: ${pokemon.level}`);
      logger.info(`  experience: ${pokemon.experience}`);
      logger.info(`  image: ${pokemon.image || '(none)'}`);
    });

    // Test the time-events storage functions
    logger.info('Testing time-events storage functions...');

    // Save Pokemon using saveSpawnsToStorage
    await saveSpawnsToStorage(testPokemons);

    // Load Pokemon using loadSpawnsFromStorage
    const timeEventPokemons = await loadSpawnsFromStorage();
    logger.info(`Loaded ${timeEventPokemons.length} Pokemon from time-events storage`);

    // Recreate Pokemon objects from time-events data
    const recreatedTimeEventPokemons = timeEventPokemons.map(data => Pokemon.fromJSON(data));

    // Log recreated Pokemon details
    logger.info('Recreated time-event Pokemon details:');
    recreatedTimeEventPokemons.forEach((pokemon, index) => {
      logger.info(`Pokemon ${index + 1}: ${pokemon.name} (ID: ${pokemon.id})`);
      logger.info(`  dex_number: ${pokemon.dex_number}`);
      logger.info(`  types: ${pokemon.types.join(', ')}`);
      logger.info(`  level: ${pokemon.level}`);
      logger.info(`  experience: ${pokemon.experience}`);
      logger.info(`  image: ${pokemon.image || '(none)'}`);
    });

    // Clean up test data
    await storageService.remove(TEST_STORAGE_KEY);

    logger.info('Pokemon storage test completed successfully');
  } catch (error) {
    logger.error('Error in Pokemon storage test:', error);
  }
}

/**
 * Load pokedex data from JSON file
 */
async function loadPokedexData() {
  try {
    const response = await fetch('../pokedex-151.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    pokedexData = await response.json();

    // Also set it in gameState for compatibility
    gameState.pokedexData = pokedexData;

    logger.info(`Loaded pokedex data with ${pokedexData.length} Pokemon`);
  } catch (error) {
    logger.error('Error loading pokedex data:', error);
    pokedexData = [];
  }
}

/**
 * Create a test Pokemon with proper data from pokedex
 * @param {string} name - Pokemon name
 * @param {string} type - Pokemon type
 * @param {number} level - Pokemon level
 * @returns {Pokemon} - Test Pokemon
 */
function createTestPokemon(name, type, level) {
  // Find the Pokemon in the pokedex using the utility function
  const pokedexEntry = findPokemonInPokedex(name, null, pokedexData);

  if (!pokedexEntry) {
    logger.error(`Pokemon ${name} not found in pokedex`);
    return new Pokemon(name, type, level);
  }

  logger.info(`Found pokedex entry for ${name}: dex_number=${pokedexEntry.dex_number}, types=${pokedexEntry.types.join(',')}`);

  // Create Pokemon with proper data
  const pokemon = new Pokemon(
    pokedexEntry.name,
    pokedexEntry.types[0],
    level,
    {
      types: pokedexEntry.types,
      dex: pokedexEntry.dex_number,
      image_url: pokedexEntry.image_url,
      rarity: pokedexEntry.rarity || 'common',
      evolution_level: pokedexEntry.evolution_level,
      evolution_chain_id: pokedexEntry.evolution_chain_id
    }
  );

  // Explicitly set dex_number to ensure it's set correctly
  pokemon.dex_number = pokedexEntry.dex_number;

  return pokemon;
}

// Run the test when the script is loaded
runTest();
