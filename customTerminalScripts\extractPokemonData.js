const fs = require('fs');
const path = require('path');

const inputPath = path.join(__dirname, 'www/src/PokemonDataSet/PokemonDataSet.json');
const outputPath = path.join(__dirname, 'www/src/PokemonDataSet/SimplifiedPokemonData.json');

const raw = fs.readFileSync(inputPath, 'utf-8');

// Locate the "monsters" object even if file has trailing invalid content
const marker = '"monsters"';
const idx = raw.indexOf(marker);
if (idx === -1) {
  console.error('Kein "monsters"-Objekt gefunden.');
  process.exit(1);
}
// Find opening brace of monsters
let braceStart = raw.indexOf('{', idx);
let depth = 1;
let i = braceStart + 1;
for (; i < raw.length; i++) {
  if (raw[i] === '{') depth++;
  else if (raw[i] === '}') depth--;
  if (depth === 0) break;
}
if (depth !== 0) {
  console.error('Ungültige JSON-Struktur: fehlende schließende Klammern.');
  process.exit(1);
}
const objStr = raw.slice(braceStart, i + 1);
const jsonStr = `{ "monsters": ${objStr} }`;

let data;
try {
  data = JSON.parse(jsonStr);
} catch (err) {
  console.error('Fehler beim Parsen der extrahierten JSON:', err.message);
  process.exit(1);
}

const simplified = Object.values(data.monsters).map(entry => {
  const number = entry.cry;
  const form = entry.forms && entry.forms[0];
  const name = form && form.name;
  const types = form && form.types;
  const de = entry.nameStyles && entry.nameStyles.ge;
  return { number, name, types, de };
});

fs.writeFileSync(outputPath, JSON.stringify(simplified, null, 2), 'utf-8');
console.log(`Erstellt ${simplified.length} Einträge in ${outputPath}`);
