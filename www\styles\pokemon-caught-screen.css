/* pokemon-caught-screen.css */
.pokemon-caught-overlay {
  background: var(--standard-background-color, #f4f4f4);
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  z-index: 10020;
  overflow-y: auto;
  padding: 0;
}

.pokemon-caught-header {
  /* Spezifische Stile für den Pokemon-Caught-Header, falls nötig */
}

.pokemon-caught-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 100px);
  padding: 20px;
  text-align: center;
  color: var(--standard-text-color);
  font-size: 1.2rem;
}

.pokemon-team-section,
.pokemon-caught-section {
  padding: 10px 16px;
}

.pokemon-team-section h2,
.pokemon-caught-section h2 {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--standard-text-color);
  margin: 10px 0;
  padding-left: 5px;
  border-left: 4px solid var(--button-color, #4CAF50);
}

/* Override pokedex grid for the pokemon caught screen */
.pokemon-team-section .pokedex-grid,
.pokemon-caught-section .pokedex-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 14px;
  padding: 14px;
  grid-auto-rows: auto; /* Let rows size naturally */
}

.pokemon-caught-empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: var(--standard-text-color);
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  margin: 10px 0;
}

.pokemon-caught-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 100px);
  padding: 20px;
  text-align: center;
  color: var(--standard-text-color);
  font-size: 1.2rem;
}

.pokemon-name {
  font-weight: 600;
  color: #40686b;
}

.caught-date {
  font-size: 0.98rem;
  color: #888;
  margin-left: 14px;
}

/* Experience bar styling */
.pokemon-exp-container {
  margin: 5px 0;
  width: 100%;
}

.pokemon-exp-bar {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 2px;
}

.pokemon-exp-fill {
  height: 100%;
  background-color: #4a90e2; /* Blue for experience */
  border-radius: 3px;
  transition: width 0.5s ease-out;
}

.pokemon-exp-text {
  font-size: 0.7rem;
  color: #666;
  text-align: right;
}

/* Team management button styling */
.team-action-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 10px;
  padding: 0;
}

.team-action-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.team-action-btn:active {
  transform: scale(0.95);
}

.team-action-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.team-action-btn img {
  width: 24px;
  height: 24px;
}

.add-to-team-btn {
  background-color: rgba(76, 175, 80, 0.8); /* Green background */
}

.add-to-team-btn:hover {
  background-color: rgba(76, 175, 80, 1);
}

.remove-from-team-btn {
  background-color: rgba(244, 67, 54, 0.8); /* Red background */
}

.remove-from-team-btn:hover {
  background-color: rgba(244, 67, 54, 1);
}

.card-action-text {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

/* Card flip functionality */
.pokemon-card-container {
  perspective: 1000px;
  width: 100%;
  height: 0;
  padding-bottom: 120%; /* Maintain aspect ratio */
  position: relative;
  margin-bottom: 0;
}

.pokemon-card-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.pokemon-card-container.flipped .pokemon-card-inner {
  transform: rotateY(180deg);
}

.pokemon-card-front, .pokemon-card-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.pokemon-card-front {
  background-color: inherit;
  color: inherit;
  z-index: 2;
}

.pokemon-card-back {
  background-color: #f1f1f1;
  transform: rotateY(180deg);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
  z-index: 1;
}

/* Make sure the pokedex card has relative positioning for absolute buttons */
.pokedex-card {
  position: relative;
  cursor: pointer;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* Override some pokedex card styles for the flip cards */
.pokemon-card-front.pokedex-card {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

.empty-team-slot {
  height: 0;
  padding-bottom: 120%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(0, 0, 0, 0.1);
  cursor: default;
  position: relative;
}

/* Buddy Pokemon styling */
.buddy-pokemon {
  border: 3px solid #ffcc00 !important; /* Gold border for buddy */
  box-shadow: 0 0 10px rgba(255, 204, 0, 0.5) !important;
}

.buddy-label-container {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 30;
  pointer-events: none;
}

.buddy-label {
  background-color: #ffcc00;
  color: #333;
  font-size: 0.7rem;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Make buddy button */
.make-buddy-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(255, 204, 0, 0.8);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 10px;
  padding: 0;
}

.make-buddy-btn:hover {
  background-color: rgba(255, 204, 0, 1);
  transform: scale(1.1);
}

.make-buddy-btn:active {
  transform: scale(0.95);
}

.make-buddy-btn img {
  width: 24px;
  height: 24px;
}

/* Card back content styling */
.card-back-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.card-pokemon-name {
  font-weight: bold;
  font-size: 1.1rem;
  margin-bottom: 5px;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.card-pokemon-level {
  font-size: 0.9rem;
  margin-bottom: 15px;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.card-action-text {
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}
