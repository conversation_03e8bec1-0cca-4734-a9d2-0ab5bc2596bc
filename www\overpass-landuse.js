// Kleines Modul für Overpass-Landuse-Abfrage
// Nutzt fetch, gibt Promise<string> zurück (z.B. "forest", "residential", ... oder "unbekannt")

// Hilfsfunktion: Punkt-in-Polygon-Test (Raycasting-Algorithmus)
function pointInPolygon(lat, lng, polygon) {
    let inside = false;
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
        const xi = polygon[i][0], yi = polygon[i][1];
        const xj = polygon[j][0], yj = polygon[j][1];
        const intersect = ((yi > lng) !== (yj > lng)) &&
            (lat < (xj - xi) * (lng - yi) / (yj - yi + 1e-10) + xi);
        if (intersect) inside = !inside;
    }
    return inside;
}

// Liefert alle Landuse/Natural/Leisure-Polygone im Umkreis als GeoJSON FeatureCollection
export async function getLandusePolygonsGeoJSON(lat, lng, radiusMeters = 500) {
    const query = `[out:json];(
      way["landuse"](around:${radiusMeters},${lat},${lng});
      way["natural"](around:${radiusMeters},${lat},${lng});
      way["leisure"](around:${radiusMeters},${lat},${lng});
    );
    (._;>;);
    out body;`;
    const url = 'https://overpass-api.de/api/interpreter';
    try {
        const response = await fetch(url, {
            method: 'POST',
            body: query,
            headers: { 'Content-Type': 'text/plain' }
        });
        if (!response.ok) return { type: 'FeatureCollection', features: [] };
        const data = await response.json();
        if (!data.elements || data.elements.length === 0) return { type: 'FeatureCollection', features: [] };
        // Knoten-Id -> Koordinate
        const nodes = {};
        for (const el of data.elements) {
            if (el.type === 'node') {
                nodes[el.id] = [el.lon, el.lat]; // GeoJSON: [lon,lat]
            }
        }
        const features = [];
        for (const el of data.elements) {
            if (el.type === 'way' && el.nodes && el.nodes.length >= 3) {
                let typeTag = el.tags.landuse ? 'landuse' : (el.tags.natural ? 'natural' : (el.tags.leisure ? 'leisure' : null));
                let valueTag = el.tags[typeTag];
                if (!typeTag || !valueTag) continue;
                const coords = el.nodes.map(nid => nodes[nid]).filter(Boolean);
                // Polygon schließen
                if (coords.length >= 3 && (coords[0][0] !== coords[coords.length-1][0] || coords[0][1] !== coords[coords.length-1][1])) {
                    coords.push(coords[0]);
                }
                if (coords.length >= 4) {
                    features.push({
                        type: 'Feature',
                        geometry: {
                            type: 'Polygon',
                            coordinates: [coords]
                        },
                        properties: {
                            osm_id: el.id,
                            type: typeTag,
                            value: valueTag
                        }
                    });
                }
            }
        }
        return { type: 'FeatureCollection', features };
    } catch (e) {
        return { type: 'FeatureCollection', features: [] };
    }
}

export async function getLanduseForLatLng(lat, lng) {
    // Overpass QL: hole alle landuse-Polygone im Umkreis von 50m
    const query = `[out:json];(
      way["landuse"](around:50,${lat},${lng});
      way["natural"](around:50,${lat},${lng});
      way["leisure"](around:50,${lat},${lng});
    );
    (._;>;);
    out body;`;
    const url = 'https://overpass-api.de/api/interpreter';
    try {
        const response = await fetch(url, {
            method: 'POST',
            body: query,
            headers: { 'Content-Type': 'text/plain' }
        });
        if (!response.ok) return {type: null, value: 'unbekannt'};
        const data = await response.json();
        ('[OVERPASS-LANDUSE] Antwort:', data);
        if (!data.elements || data.elements.length === 0) {
            ('[OVERPASS-LANDUSE] Keine Elemente erhalten!');
            return {type: null, value: 'unbekannt'};
        }
        // Baue ein Knoten-Id -> Koordinate Mapping
        const nodes = {};
        for (const el of data.elements) {
            if (el.type === 'node') {
                nodes[el.id] = [el.lat, el.lon];
            }
        }
        let found = false;
        let polyCount = 0;
        // Prüfe für jedes Polygon, ob der Punkt enthalten ist
        for (const el of data.elements) {
            // Prüfe alle Polygone (landuse, natural, leisure)
            if (el.type === 'way' && el.tags && el.nodes && el.nodes.length >= 3) {
                // Bestimme den Typ des Polygons (landuse, natural, leisure)
                let typeTag = null;
                let valueTag = null;

                if (el.tags.landuse) {
                    typeTag = 'landuse';
                    valueTag = el.tags.landuse;
                } else if (el.tags.natural) {
                    typeTag = 'natural';
                    valueTag = el.tags.natural;
                } else if (el.tags.leisure) {
                    typeTag = 'leisure';
                    valueTag = el.tags.leisure;
                }

                // Wenn kein Typ gefunden wurde, überspringe dieses Polygon
                if (!typeTag || !valueTag) continue;

                const polygon = el.nodes.map(nid => nodes[nid]).filter(Boolean);
                polyCount++;
                console.log(`[OVERPASS-LANDUSE] Prüfe Polygon: ${typeTag}=${valueTag}`);

                if (polygon.length >= 3) {
                    // Standard-Test (lat,lon)
                    const inside = pointInPolygon(lat, lng, polygon);
                    // Alternativ: Test mit vertauschter Reihenfolge (lon,lat)
                    let insideSwapped = false;
                    if (!inside) {
                        const swappedPoly = polygon.map(([a,b]) => [b,a]);
                        insideSwapped = pointInPolygon(lng, lat, swappedPoly);
                    }

                    console.log(`[OVERPASS-LANDUSE] Punkt-in-Polygon: ${inside} (swapped: ${insideSwapped}) für Typ ${typeTag}=${valueTag}`);

                    if (inside || insideSwapped) {
                        found = true;
                        return {type: typeTag, value: valueTag};
                    }
                }
            }
        }
        (`[OVERPASS-LANDUSE] Anzahl geprüfter Polygone: ${polyCount}`);
        if (!found) {
            ('[OVERPASS-LANDUSE] Kein passendes Polygon gefunden!');
        }
        return {type: null, value: 'unbekannt'};
    } catch (e) {
        return {type: null, value: 'unbekannt'};
    }
}

