/**
 * Player Renderer
 * Handles rendering and animation of the player sprite on the map
 */

import { gameState } from '../state/game-state.js';
import { config } from '../config.js';

export class PlayerRenderer {
  constructor() {
    this.playerMarker = null;
    this.playerSprites = [];
    this.currentDirection = 'n'; // Default direction
    this.isMoving = false;
    this.gender = 'm'; // Default to male
    this.spriteBasePath = '../src/PlayerSprites/male/';
    this.currentFrame = 1;
    this.animationInterval = null;
    this.isAnimating = false;
    this.lastPosition = null;
    this.targetPosition = null;
    this.animationStep = 0;
    this.animationSteps = 30; // Total steps for smooth animation
  }

  /**
   * Initialize the player sprite
   * @param {number} lat - Initial latitude
   * @param {number} lng - Initial longitude
   * @param {string} gender - Player gender ('m' or 'f')
   */
  initPlayerSprite(lat, lng, gender = 'm') {
    // Check if player sprite should be shown
    if (!config.ui.showPlayerSprite) {
      return;
    }

    if (this.playerMarker) {
      return; // Already initialized
    }

    this.gender = gender;
    // Use absolute path from www root
    this.spriteBasePath = gender === 'f' ? './src/PlayerSprites/female/' : './src/PlayerSprites/male/';
    console.log('Sprite base path:', this.spriteBasePath);
    this.lastPosition = { lat, lng };

    // Create container for all sprite frames
    const container = document.createElement('div');
    container.className = 'player-container';

    // Create three sprite elements for animation frames
    for (let i = 1; i <= 3; i++) {
      const sprite = document.createElement('div');
      // Add player-sprite-static class to all frames initially
      sprite.className = `player-sprite player-sprite-frame${i} player-sprite-static`;
      sprite.id = `player-sprite-${i}`;

      // Set initial visibility - only first frame visible
      if (i === 1) {
        sprite.style.opacity = '1';
      } else {
        sprite.style.opacity = '0';
      }

      container.appendChild(sprite);
      this.playerSprites.push(sprite);
    }

    // Create a Leaflet divIcon with the container
    const { playerSizeWidth, playerSizeHeight } = config.ui;
    console.log('Player size:', playerSizeWidth, 'x', playerSizeHeight);

    // Use the exact configured size
    const iconSize = [playerSizeWidth, playerSizeHeight];
    console.log('Using icon size:', iconSize);

    const playerIcon = L.divIcon({
      className: 'player-icon-container',
      html: container,
      iconSize: iconSize,
      iconAnchor: [iconSize[0] / 2, iconSize[1] / 2]
    });

    // Create the marker and add it to the map
    this.playerMarker = L.marker([lat, lng], {
      icon: playerIcon,
      interactive: false,
      zIndexOffset: 1000 // Make sure player is above other markers
    }).addTo(gameState.map);

    // Set initial direction
    this.updateDirection('n');

    // Explicitly set the initial state to not moving
    this.isMoving = false;
    this.setMoving(false); // This will stop the animation

    console.log('Player sprite created at', lat, lng);
  }

  /**
   * Update player position with animation
   * @param {number} lat - Target latitude
   * @param {number} lng - Target longitude
   */
  updatePosition(lat, lng) {
    if (!this.playerMarker) return;

    // If we're already at this position, just update direction
    if (this.lastPosition &&
        this.lastPosition.lat === lat &&
        this.lastPosition.lng === lng) {
      return;
    }

    // If we're already animating, update the target
    if (this.isAnimating) {
      this.targetPosition = { lat, lng };
      return;
    }

    // Start a new animation
    this.targetPosition = { lat, lng };
    this.animationStep = 0;
    this.isAnimating = true;

    // Calculate direction before starting animation
    if (this.lastPosition) {
      const heading = this.calculateHeading(
        this.lastPosition.lat,
        this.lastPosition.lng,
        lat,
        lng
      );
      this.updateDirection(heading);
    }

    // Start walking animation
    this.setMoving(true);

    // Start animation loop
    this.animateMovement();
  }

  /**
   * Calculate heading between two points
   * @param {number} lat1 - Start latitude
   * @param {number} lng1 - Start longitude
   * @param {number} lat2 - End latitude
   * @param {number} lng2 - End longitude
   * @returns {number} - Heading in degrees (0-360)
   */
  calculateHeading(lat1, lng1, lat2, lng2) {
    const toRad = deg => deg * Math.PI / 180;
    const toDeg = rad => rad * 180 / Math.PI;
    const dLon = toRad(lng2 - lng1);
    const y = Math.sin(dLon) * Math.cos(toRad(lat2));
    const x = Math.cos(toRad(lat1)) * Math.sin(toRad(lat2)) -
              Math.sin(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.cos(dLon);
    let brng = Math.atan2(y, x);
    brng = toDeg(brng);
    return (brng + 360) % 360;
  }

  /**
   * Animate player movement from current to target position
   */
  animateMovement() {
    if (!this.isAnimating || !this.lastPosition || !this.targetPosition) return;

    this.animationStep++;
    const progress = this.animationStep / this.animationSteps;

    // Calculate intermediate position
    const lat = this.lastPosition.lat + (this.targetPosition.lat - this.lastPosition.lat) * progress;
    const lng = this.lastPosition.lng + (this.targetPosition.lng - this.lastPosition.lng) * progress;

    // Update marker position
    this.playerMarker.setLatLng([lat, lng]);

    if (this.animationStep < this.animationSteps) {
      // Continue animation
      requestAnimationFrame(() => this.animateMovement());
    } else {
      // Animation complete
      this.lastPosition = { ...this.targetPosition };
      this.isAnimating = false;
      this.setMoving(false);

      // Check if we have a new target (in case position was updated during animation)
      if (this.targetPosition &&
          (this.lastPosition.lat !== this.targetPosition.lat ||
           this.lastPosition.lng !== this.targetPosition.lng)) {
        // Start a new animation to the updated target
        this.updatePosition(this.targetPosition.lat, this.targetPosition.lng);
      }
    }
  }

  /**
   * Update player direction based on heading
   * @param {number} heading - Heading in degrees (0-360)
   */
  updateDirection(heading) {
    // Convert heading to direction code
    const direction = this.headingToDirection(heading);

    if (direction !== this.currentDirection) {
      this.currentDirection = direction;
      this.updateSprites();
    }
  }

  /**
   * Convert heading to direction code
   * @param {number|string} heading - Heading in degrees (0-360) or direction code
   * @returns {string} - Direction code (n, ne, e, se, s, sw, w, nw)
   */
  headingToDirection(heading) {
    // If heading is already a direction string, return it
    if (typeof heading === 'string' && ['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'].includes(heading.toLowerCase())) {
      return heading.toLowerCase();
    }

    // Convert numeric heading to direction
    const dirs = ['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'];
    const idx = Math.round(heading / 45) % 8;
    return dirs[idx];
  }

  /**
   * Update sprite images based on current direction
   */
  updateSprites() {
    if (!this.playerSprites.length) return;

    for (let i = 0; i < 3; i++) {
      const frameNum = i + 1;
      const sprite = this.playerSprites[i];
      const imgPath = `${this.spriteBasePath}player${this.gender}_${this.currentDirection} (${frameNum}).png`;
      sprite.style.backgroundImage = `url('${imgPath}')`;

      // Log the first sprite path to check if it's correct
      if (i === 0) {
        console.log('Loading sprite image:', imgPath);

        // Create a test image to check if the path is valid
        const testImg = new Image();
        testImg.onload = () => console.log('Sprite image loaded successfully');
        testImg.onerror = () => console.error('Failed to load sprite image:', imgPath);
        testImg.src = imgPath;
      }
    }
  }

  /**
   * Set player movement state
   * @param {boolean} isMoving - Whether the player is moving
   */
  setMoving(isMoving) {
    if (this.isMoving === isMoving) return;

    this.isMoving = isMoving;

    if (!this.playerSprites.length) return;

    console.log('Setting player movement state:', isMoving ? 'moving' : 'static');

    if (isMoving) {
      // Start animation - remove static class
      this.playerSprites.forEach((sprite) => {
        sprite.classList.remove('player-sprite-static');
      });
    } else {
      // Stop animation, show only first frame
      this.playerSprites.forEach((sprite, index) => {
        sprite.classList.add('player-sprite-static');

        // Make only the first frame visible
        if (index === 0) {
          sprite.style.opacity = '1';
        } else {
          sprite.style.opacity = '0';
        }
      });
    }
  }
}

// Create and export a singleton instance
export const playerRenderer = new PlayerRenderer();
