const fs = require('fs');
const path = require('path');

// Path to simplified data and sprites folder
const dataPath = path.join(__dirname, 'www/src/PokemonDataSet/SimplifiedPokemonData.json');
const spritesDir = 'www/src/PokemonSprites';

// Read existing JSON
let entries;
try {
  entries = JSON.parse(fs.readFileSync(dataPath, 'utf-8'));
} catch (err) {
  console.error('Failed to load simplified data:', err.message);
  process.exit(1);
}

// Add img field
const updated = entries.map(entry => {
  // remove leading zeros
  const num = String(parseInt(entry.number, 10));
  const img = path.posix.join(spritesDir, `${num}.png`);
  return { ...entry, img };
});

// Write back
fs.writeFileSync(dataPath, JSON.stringify(updated, null, 2), 'utf-8');
console.log(`Added img for ${updated.length} entries in ${dataPath}`);
