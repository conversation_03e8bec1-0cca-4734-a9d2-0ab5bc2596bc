// merge-evolutions-into-pokedex.js
import fs from "fs/promises";
import fetch from "node-fetch";

// Pfade ggf. anpassen
const pokedexPath = './www/pokedex-151.json';
const evolutionsPath = './www/evolutions.json';

async function mergeEvolutions() {
    const pokedex = JSON.parse(await fs.readFile(pokedexPath, 'utf8'));
    const evolutions = JSON.parse(await fs.readFile(evolutionsPath, 'utf8'));

    // Erstelle ein Lookup für die Evolutionsdaten
    const evoMap = {};
    for (const evo of evolutions) {
        evoMap[evo.name.toLowerCase()] = {
            evolution_level: evo.evolution_level ?? null,
            evolution_item: evo.evolution_item ?? null
        };
    }

    // Füge die Daten in die pokedex-Objekte ein
    for (const p of pokedex) {
        const evo = evoMap[p.name.toLowerCase()];
        p.evolution_level = evo ? evo.evolution_level : null;
        p.evolution_item = evo ? evo.evolution_item : null;
    }

    // Schreibe das Ergebnis in eine neue Datei
    await fs.writeFile('./www/pokedex-151-evo.json', JSON.stringify(pokedex, null, 2), 'utf8');
    console.log('Fertig! Neue Datei: pokedex-151-evo.json');
}

mergeEvolutions();