// services/pokemon-manager.js
// Central manager for all Pokemon data in the application

import { config } from '../config.js';
import { storageService } from '../storage/storage-service.js';
import { logger } from '../utils/logger.js';
import { default as Pokemon } from '../Pokemon.js';

// Storage key for all Pokemon
const STORAGE_KEY = 'allPokemon';

/**
 * PokemonManager class
 * Central manager for all Pokemon data in the application
 */
class PokemonManager {
  constructor() {
    this.allPokemon = [];
    this.isInitialized = false;
    this.teamIds = new Set(); // IDs of Pokemon in the team
  }

  /**
   * Initialize the Pokemon manager
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Import Pokemon class
      const Pokemon = (await import('../Pokemon.js')).default;

      // Load all Pokemon from storage
      const storedPokemon = await storageService.get(STORAGE_KEY, []);
      logger.debug(`Loaded ${storedPokemon.length} Pokemon from storage`);

      // Convert plain objects to Pokemon instances
      this.allPokemon = storedPokemon.map(data => {
        try {
          // Check if the data already has the proper methods (is already a Pokemon instance)
          if (typeof data.addExperience === 'function') {
            return data;
          }

          // Otherwise, create a new Pokemon instance from the data
          return Pokemon.fromJSON(data);
        } catch (e) {
          logger.error(`Error creating Pokemon instance from data:`, e, data);
          return data; // Return the original data as fallback
        }
      });

      // Log details of loaded Pokemon for debugging
      if (this.allPokemon.length > 0) {
        logger.debug('Sample of loaded Pokemon:');
        for (let i = 0; i < Math.min(5, this.allPokemon.length); i++) {
          const pokemon = this.allPokemon[i];
          logger.debug(`  ${pokemon.name} (ID: ${pokemon.id}): Level ${pokemon.level}, XP ${pokemon.experience}`);
        }
      }

      // Load team IDs
      const teamPokemon = await storageService.get(config.storage.teamPokemonKey, []);
      this.teamIds = new Set(teamPokemon.map(p => p.id));
      logger.debug(`Loaded ${this.teamIds.size} Pokemon in team`);

      // Migrate data from old storage if needed
      await this.migrateFromOldStorage();

      // Validate and fix experience values for all Pokemon
      await this.validatePokemonExperience();

      // Log details after validation for debugging
      if (this.allPokemon.length > 0) {
        logger.debug('Sample of Pokemon after validation:');
        for (let i = 0; i < Math.min(5, this.allPokemon.length); i++) {
          const pokemon = this.allPokemon[i];
          logger.debug(`  ${pokemon.name} (ID: ${pokemon.id}): Level ${pokemon.level}, XP ${pokemon.experience}, dex_number: ${pokemon.dex_number}`);
        }
      }

      this.isInitialized = true;
    } catch (e) {
      logger.error('Error initializing Pokemon manager:', e);
      this.allPokemon = [];
      this.teamIds = new Set();
    }
  }



  /**
   * Migrate data from old storage format
   * @returns {Promise<void>}
   */
  async migrateFromOldStorage() {
    try {
      // Check if we already have Pokemon in the new storage
      if (this.allPokemon.length > 0) {
        logger.debug('Pokemon already exist in new storage, skipping migration');
        return;
      }

      // Migrate caught Pokemon
      const caughtPokemon = await storageService.get(config.storage.caughtPokemonKey, []);
      if (caughtPokemon.length > 0) {
        logger.debug(`Migrating ${caughtPokemon.length} caught Pokemon to new storage`);

        // Add all caught Pokemon to the new storage
        for (const pokemon of caughtPokemon) {
          // Make sure we don't add duplicates
          if (!this.allPokemon.some(p => p.id === pokemon.id)) {
            this.allPokemon.push(pokemon);
          }
        }
      }

      // Migrate team Pokemon
      const teamPokemon = await storageService.get(config.storage.teamPokemonKey, []);
      if (teamPokemon.length > 0) {
        logger.debug(`Migrating ${teamPokemon.length} team Pokemon to new storage`);

        // Add all team Pokemon to the new storage
        for (const pokemon of teamPokemon) {
          // Update the teamIds set
          this.teamIds.add(pokemon.id);

          // Check if this Pokemon is already in allPokemon
          const existingIndex = this.allPokemon.findIndex(p => p.id === pokemon.id);

          if (existingIndex >= 0) {
            // Update the existing Pokemon with team data (which might have more recent XP/level)
            this.allPokemon[existingIndex] = pokemon;
          } else {
            // Add the Pokemon to allPokemon
            this.allPokemon.push(pokemon);
          }
        }
      }

      // Save the migrated data
      if (this.allPokemon.length > 0) {
        await this.saveAllPokemon();
        logger.debug(`Migrated ${this.allPokemon.length} Pokemon to new storage`);
      }
    } catch (e) {
      logger.error('Error migrating from old storage:', e);
    }
  }

  /**
   * Save all Pokemon to storage
   * @returns {Promise<boolean>}
   */
  async saveAllPokemon() {
    try {
      return await storageService.set(STORAGE_KEY, this.allPokemon);
    } catch (e) {
      logger.error('Error saving all Pokemon:', e);
      return false;
    }
  }

  /**
   * Get all Pokemon
   * @returns {Array} - All Pokemon
   */
  getAllPokemon() {
    return [...this.allPokemon];
  }

  /**
   * Get caught Pokemon (all Pokemon that are not in the team)
   * @returns {Array} - Caught Pokemon
   */
  getCaughtPokemon() {
    return this.allPokemon.filter(p => !this.teamIds.has(p.id));
  }

  /**
   * Get team Pokemon
   * @returns {Array} - Team Pokemon
   */
  getTeamPokemon() {
    // Get all Pokemon that are in the team
    const teamPokemon = this.allPokemon.filter(p => this.teamIds.has(p.id));

    // Sort them according to the order in teamIds
    // This is important for buddy functionality (first Pokemon is the buddy)
    const teamIds = Array.from(this.teamIds);
    teamPokemon.sort((a, b) => {
      const indexA = teamIds.indexOf(a.id);
      const indexB = teamIds.indexOf(b.id);
      return indexA - indexB;
    });

    logger.debug(`getTeamPokemon returning ${teamPokemon.length} Pokemon: ${teamPokemon.map(p => p.name).join(', ')}`);
    return teamPokemon;
  }

  /**
   * Get a Pokemon by ID
   * @param {string} id - The Pokemon ID
   * @returns {Object|null} - The Pokemon or null if not found
   */
  getPokemonById(id) {
    return this.allPokemon.find(p => p.id === id) || null;
  }

  /**
   * Add a Pokemon
   * @param {Object} pokemon - The Pokemon to add
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async addPokemon(pokemon) {
    try {
      // Check if Pokemon already exists
      const existingIndex = this.allPokemon.findIndex(p => p.id === pokemon.id);

      if (existingIndex >= 0) {
        // Update existing Pokemon
        this.allPokemon[existingIndex] = pokemon;
        logger.debug(`Updated existing Pokemon ${pokemon.name} (ID: ${pokemon.id}) in allPokemon`);
      } else {
        // Add new Pokemon
        this.allPokemon.push(pokemon);
        logger.debug(`Added new Pokemon ${pokemon.name} (ID: ${pokemon.id}) to allPokemon`);
      }

      // Log the Pokemon's experience
      if (pokemon.experience !== undefined) {
        logger.debug(`Pokemon ${pokemon.name} has ${pokemon.experience} XP (Level ${pokemon.level})`);
      }

      return await this.saveAllPokemon();
    } catch (e) {
      logger.error('Error adding Pokemon:', e);
      return false;
    }
  }

  /**
   * Update a Pokemon
   * @param {Object} pokemon - The Pokemon to update
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async updatePokemon(pokemon) {
    try {
      const index = this.allPokemon.findIndex(p => p.id === pokemon.id);

      if (index === -1) {
        logger.warn(`Pokemon with ID ${pokemon.id} not found, adding it instead`);
        return await this.addPokemon(pokemon);
      }

      // Get the existing Pokemon
      const existingPokemon = this.allPokemon[index];

      // Log the update details for debugging
      logger.debug(`Updating Pokemon ${pokemon.name} (ID: ${pokemon.id})`);
      logger.debug(`  Level: ${existingPokemon.level} -> ${pokemon.level}`);
      logger.debug(`  XP: ${existingPokemon.experience} -> ${pokemon.experience}`);

      // Update the Pokemon
      this.allPokemon[index] = pokemon;

      // Save immediately to ensure XP values are persisted
      const result = await this.saveAllPokemon();

      if (result) {
        logger.debug(`Successfully saved updated Pokemon ${pokemon.name} with level ${pokemon.level} and XP ${pokemon.experience}`);

        // Check if this Pokemon is in the team
        if (this.teamIds.has(pokemon.id)) {
          logger.debug(`Pokemon ${pokemon.name} is in the team, updating team storage as well`);

          // Update the team storage to ensure consistency
          const teamResult = await this.saveTeam();
          if (teamResult) {
            logger.debug(`Successfully updated team with Pokemon ${pokemon.name}'s new data`);
          } else {
            logger.error(`Failed to update team with Pokemon ${pokemon.name}'s new data`);
          }
        }
      } else {
        logger.error(`Failed to save updated Pokemon ${pokemon.name}`);
      }

      return result;
    } catch (e) {
      logger.error('Error updating Pokemon:', e);
      return false;
    }
  }

  /**
   * Remove a Pokemon
   * @param {string} id - The ID of the Pokemon to remove
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async removePokemon(id) {
    try {
      const index = this.allPokemon.findIndex(p => p.id === id);

      if (index === -1) {
        logger.warn(`Pokemon with ID ${id} not found, nothing to remove`);
        return false;
      }

      // Remove from team if it's in the team
      if (this.teamIds.has(id)) {
        this.teamIds.delete(id);
        await this.saveTeam();
      }

      // Remove the Pokemon
      this.allPokemon.splice(index, 1);

      return await this.saveAllPokemon();
    } catch (e) {
      logger.error('Error removing Pokemon:', e);
      return false;
    }
  }

  /**
   * Add a Pokemon to the team
   * @param {string} id - The ID of the Pokemon to add to the team
   * @returns {Promise<{success: boolean, message: string, pokemon: Object|null}>} - Result with success status, message and Pokemon
   */
  async addToTeam(id) {
    try {
      // Check if team is already full
      if (this.teamIds.size >= 6) {
        return {
          success: false,
          message: 'Team is already full (max 6 Pokémon)',
          pokemon: null
        };
      }

      // Check if Pokemon is already in team
      if (this.teamIds.has(id)) {
        const pokemon = this.getPokemonById(id);
        return {
          success: false,
          message: `${pokemon?.name || 'Pokemon'} is already in your team`,
          pokemon: null
        };
      }

      // Find the Pokemon
      const pokemon = this.getPokemonById(id);
      if (!pokemon) {
        return {
          success: false,
          message: 'Pokemon not found',
          pokemon: null
        };
      }

      // Add to team
      this.teamIds.add(id);

      // Save the team
      const saved = await this.saveTeam();
      if (saved) {
        return {
          success: true,
          message: `${pokemon.name} was added to your team`,
          pokemon
        };
      } else {
        return {
          success: false,
          message: 'Failed to save team',
          pokemon: null
        };
      }
    } catch (e) {
      logger.error('Error adding Pokemon to team:', e);
      return {
        success: false,
        message: 'An error occurred while adding to team',
        pokemon: null
      };
    }
  }

  /**
   * Remove a Pokemon from the team
   * @param {string} id - The ID of the Pokemon to remove from the team
   * @returns {Promise<{success: boolean, message: string, pokemon: Object|null}>} - Result with success status, message and removed Pokemon
   */
  async removeFromTeam(id) {
    try {
      // Check if Pokemon is in team
      if (!this.teamIds.has(id)) {
        return {
          success: false,
          message: 'Pokemon not found in team',
          pokemon: null
        };
      }

      // Find the Pokemon
      const pokemon = this.getPokemonById(id);
      if (!pokemon) {
        return {
          success: false,
          message: 'Pokemon not found',
          pokemon: null
        };
      }

      // Remove from team
      this.teamIds.delete(id);

      // Save the team
      const saved = await this.saveTeam();
      if (saved) {
        return {
          success: true,
          message: `${pokemon.name} was removed from your team`,
          pokemon
        };
      } else {
        return {
          success: false,
          message: 'Failed to save team',
          pokemon: null
        };
      }
    } catch (e) {
      logger.error('Error removing Pokemon from team:', e);
      return {
        success: false,
        message: 'An error occurred while removing from team',
        pokemon: null
      };
    }
  }

  /**
   * Save the team
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async saveTeam() {
    try {
      // Get all team Pokemon
      const teamPokemon = this.getTeamPokemon();
      logger.debug(`Saving team with ${teamPokemon.length} Pokemon: ${teamPokemon.map(p => p.name).join(', ')}`);

      // Save to storage
      const result = await storageService.set(config.storage.teamPokemonKey, teamPokemon);
      logger.debug(`Team save result: ${result}`);
      return result;
    } catch (e) {
      logger.error('Error saving team:', e);
      return false;
    }
  }

  /**
   * Validate and fix experience values and image properties for all Pokemon
   * @returns {Promise<void>}
   */
  async validatePokemonExperience() {
    try {
      // Import the experience system
      const { experienceSystem, getExpForLevel, getLevelForExp } = await import('../services/experience-system.js');
      let fixedCount = 0;

      // Check each Pokemon
      for (let i = 0; i < this.allPokemon.length; i++) {
        const pokemon = this.allPokemon[i];

        // Skip if no level
        if (typeof pokemon.level !== 'number' || pokemon.level <= 0) continue;

        // Get the current level's base experience
        const curve = experienceSystem.getExpCurve(pokemon);
        const currentLevelExp = getExpForLevel(pokemon.level, curve);
        const nextLevelExp = getExpForLevel(pokemon.level + 1, curve);

        // Handle missing or negative experience values
        if (typeof pokemon.experience !== 'number') {
          // If experience is missing, log it
          logger.debug(`Found missing experience for ${pokemon.name} (Level ${pokemon.level})`);

          // Set to the base experience for the current level
          this.allPokemon[i].experience = currentLevelExp;
          logger.debug(`Fixed missing experience for ${pokemon.name} (Level ${pokemon.level}): set to ${currentLevelExp}`);
          fixedCount++;
        } else if (pokemon.experience < 0) {
          // If experience is negative, log it
          logger.debug(`Found negative experience for ${pokemon.name} (Level ${pokemon.level}): ${pokemon.experience}`);

          // Set to the base experience for the current level
          this.allPokemon[i].experience = currentLevelExp;
          logger.debug(`Fixed negative experience for ${pokemon.name} (Level ${pokemon.level}): ${pokemon.experience} -> ${currentLevelExp}`);
          fixedCount++;
        }
        // Handle experience values that are inconsistent with the Pokemon's level
        else {
          // Calculate what level the Pokemon should be based on its experience
          const calculatedLevel = getLevelForExp(pokemon.experience, curve);

          // If the calculated level doesn't match the stored level
          if (calculatedLevel !== pokemon.level) {
            logger.debug(`Experience-level mismatch for ${pokemon.name}: XP ${pokemon.experience} corresponds to level ${calculatedLevel}, but stored level is ${pokemon.level}`);

            // If the calculated level is higher, update the level
            if (calculatedLevel > pokemon.level) {
              logger.debug(`Updating ${pokemon.name}'s level from ${pokemon.level} to ${calculatedLevel} based on XP ${pokemon.experience}`);
              this.allPokemon[i].level = calculatedLevel;
              fixedCount++;
            }
            // If the calculated level is lower, the XP might be too low for the current level
            else if (pokemon.experience < currentLevelExp) {
              logger.debug(`XP too low for current level: ${pokemon.name} has ${pokemon.experience} XP but needs at least ${currentLevelExp} for level ${pokemon.level}`);
              this.allPokemon[i].experience = currentLevelExp;
              fixedCount++;
            }
          }

          // Ensure the experience is at least the minimum for the current level
          // This should not reset progress within a level
          if (pokemon.experience < currentLevelExp) {
            logger.debug(`Experience too low for ${pokemon.name} (Level ${pokemon.level}): ${pokemon.experience} < ${currentLevelExp}`);
            this.allPokemon[i].experience = currentLevelExp;
            fixedCount++;
          }

          // Ensure the experience is not higher than the next level's threshold
          // This prevents skipping levels
          if (pokemon.experience >= nextLevelExp) {
            logger.debug(`Experience too high for ${pokemon.name} (Level ${pokemon.level}): ${pokemon.experience} >= ${nextLevelExp}`);
            // Update the level instead of reducing the XP
            const newLevel = getLevelForExp(pokemon.experience, curve);
            logger.debug(`Updating level from ${pokemon.level} to ${newLevel} based on XP ${pokemon.experience}`);
            this.allPokemon[i].level = newLevel;
            fixedCount++;
          }
        }

        // Fix missing image properties
        let imageFixed = false;

        // Check if image property is missing
        if (!pokemon.image) {
          // Try to set image from other properties
          if (pokemon.image_url) {
            this.allPokemon[i].image = pokemon.image_url;
            imageFixed = true;
          } else if (pokemon.base_sprite) {
            this.allPokemon[i].image = pokemon.base_sprite;
            imageFixed = true;
          } else if (pokemon.dex_number) {
            this.allPokemon[i].image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
            imageFixed = true;
          }

          if (imageFixed) {
            fixedCount++;
          }
        }
      }

      // Save if any Pokemon were fixed
      if (fixedCount > 0) {
        logger.debug(`Fixed experience/level/image values for ${fixedCount} Pokemon`);
        await this.saveAllPokemon();
      }
    } catch (e) {
      logger.error('Error validating Pokemon experience:', e);
    }
  }

  /**
   * Make a Pokemon the buddy (first in team)
   * @param {string} id - The ID of the Pokemon to make buddy
   * @returns {Promise<{success: boolean, message: string}>} - Result with success status and message
   */
  async makePokemonBuddy(id) {
    try {
      logger.debug(`PokemonManager.makePokemonBuddy called with ID: ${id}`);

      // Check if Pokemon is in team
      if (!this.teamIds.has(id)) {
        logger.warn(`Pokemon with ID ${id} not found in team IDs: ${Array.from(this.teamIds).join(', ')}`);
        return {
          success: false,
          message: 'Pokemon not found in team'
        };
      }

      // Get team Pokemon
      const teamPokemon = this.getTeamPokemon();
      logger.debug(`Current team order: ${teamPokemon.map(p => p.name).join(', ')}`);

      // Find the Pokemon index
      const index = teamPokemon.findIndex(p => p.id === id);
      logger.debug(`Pokemon index in team: ${index}`);

      // If already at first position, do nothing
      if (index === 0) {
        logger.debug(`${teamPokemon[0].name} is already the buddy (at index 0)`);
        return {
          success: true,
          message: `${teamPokemon[0].name} is already your buddy`
        };
      }

      // Remove from current position
      const pokemon = teamPokemon.splice(index, 1)[0];
      logger.debug(`Removed ${pokemon.name} from position ${index}`);

      // Add to first position
      teamPokemon.unshift(pokemon);
      logger.debug(`Added ${pokemon.name} to first position`);
      logger.debug(`New team order: ${teamPokemon.map(p => p.name).join(', ')}`);

      // Update team IDs to maintain order
      const oldTeamIds = Array.from(this.teamIds);
      this.teamIds = new Set(teamPokemon.map(p => p.id));
      logger.debug(`Updated team IDs from [${oldTeamIds.join(', ')}] to [${Array.from(this.teamIds).join(', ')}]`);

      // Save the team
      const saved = await this.saveTeam();
      if (saved) {
        logger.debug(`Successfully saved team with ${pokemon.name} as buddy`);
        return {
          success: true,
          message: `${pokemon.name} is now your buddy`
        };
      } else {
        logger.error('Failed to save team after making buddy');
        return {
          success: false,
          message: 'Failed to save team'
        };
      }
    } catch (e) {
      logger.error('Error making Pokemon buddy:', e);
      return {
        success: false,
        message: 'An error occurred while updating buddy'
      };
    }
  }
}

// Export a singleton instance
export const pokemonManager = new PokemonManager();
