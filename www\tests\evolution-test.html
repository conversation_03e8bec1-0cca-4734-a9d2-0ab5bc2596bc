<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pokémon Evolution Test</title>
    <link rel="stylesheet" href="../styles/style.css">
    <link rel="stylesheet" href="../styles/variables-gui.css">
    <link rel="stylesheet" href="../styles/battle-screen.css">
    <link rel="stylesheet" href="../styles/pokemon-caught-screen.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-container {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-description {
            margin-bottom: 15px;
            color: #555;
        }
        .test-button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button.battle {
            background-color: #f44336;
        }
        .test-button.battle:hover {
            background-color: #d32f2f;
        }
        .test-button.caught {
            background-color: #2196F3;
        }
        .test-button.caught:hover {
            background-color: #0b7dda;
        }
        .test-results {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            min-height: 50px;
        }
        .log-entry {
            margin: 5px 0;
            font-family: monospace;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>Pokémon Evolution Test</h1>
    <p>Diese Seite testet die Evolution von Pokémon, insbesondere die Entwicklung von Pikachu zu Raichu.</p>

    <div class="test-container">
        <div class="test-title">Test 1: Pikachu Evolution im Battle Screen</div>
        <div class="test-description">
            Dieser Test simuliert einen Kampf, bei dem Pikachu genug Erfahrung sammelt, um Level 32 zu erreichen und sich zu Raichu zu entwickeln.
        </div>
        <button id="test-battle-button" class="test-button battle">Battle Screen Test starten</button>
        <div id="battle-test-results" class="test-results"></div>
    </div>

    <div class="test-container">
        <div class="test-title">Test 2: Pikachu Evolution im Pokemon Caught Screen</div>
        <div class="test-description">
            Dieser Test zeigt, wie ein frisch entwickeltes Raichu im Pokemon Caught Screen aussieht.
        </div>
        <button id="test-caught-button" class="test-button caught">Caught Screen Test starten</button>
        <div id="caught-test-results" class="test-results"></div>
    </div>

    <script type="module">
        import { openBattleScreenWithCallback } from '../ui/BattleScreen.js';
        import { showPokemonCaughtScreen } from '../ui/PokemonCaughtScreen.js';
        import { gameState } from '../state/game-state.js';
        import Pokemon from '../Pokemon.js';
        import { logger } from '../utils/logger.js';
        import { loadTeam } from '../storage/teamStorage.js';
        import { pokemonManager } from '../services/pokemon-manager.js';

        // Create a mock FAB manager for testing
        window.mockFabManager = {
            hideAllButtons: function() {
                console.log('Mock FAB Manager: hideAllButtons called');
            },
            showAllButtons: function() {
                console.log('Mock FAB Manager: showAllButtons called');
            }
        };

        // Patch the dynamic import of FabManager to use our mock if the real one fails
        const originalImport = window.import;
        window.import = function(path) {
            if (path === '../ui/FabManager.js') {
                return Promise.resolve({ fabManager: window.mockFabManager });
            }
            return originalImport.apply(this, arguments);
        };

        // Override logger to also log to our test results
        const originalDebug = logger.debug;
        const originalError = logger.error;
        const originalInfo = logger.info;

        function logToResults(message, type, container) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = message;
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }

        // Get the active results container
        function getActiveResultsContainer() {
            // Check which test was last clicked
            const battleResults = document.getElementById('battle-test-results');
            const caughtResults = document.getElementById('caught-test-results');

            // If caught results has content and battle results doesn't, use caught results
            if (caughtResults.children.length > 0 && battleResults.children.length === 0) {
                return caughtResults;
            }

            // Default to battle results
            return battleResults;
        }

        logger.debug = function(message) {
            originalDebug.apply(logger, arguments);
            logToResults(`DEBUG: ${message}`, 'info', getActiveResultsContainer());
        };

        logger.error = function(message) {
            originalError.apply(logger, arguments);
            logToResults(`ERROR: ${message}`, 'error', getActiveResultsContainer());
        };

        logger.info = function(message) {
            originalInfo.apply(logger, arguments);
            logToResults(`INFO: ${message}`, 'success', getActiveResultsContainer());
        };

        // Initialize gameState and load required data
        async function initializeTest() {
            try {
                const resultsContainer = document.getElementById('battle-test-results');

                // Load pokedex data if not already loaded
                if (!gameState.pokedexData || gameState.pokedexData.length === 0) {
                    try {
                        // Override the loadPokedexData method to use the correct path
                        const originalLoadPokedexData = gameState.loadPokedexData;

                        // Create a custom loader for the test
                        gameState.loadPokedexData = async function() {
                            try {
                                // Try different paths to find the pokedex data
                                let response;
                                let pokedexPath = '';

                                try {
                                    // First try the path relative to the test directory
                                    pokedexPath = '../pokedex-151.json';
                                    response = await fetch(pokedexPath);
                                    if (!response.ok) {
                                        throw new Error(`HTTP error! status: ${response.status}`);
                                    }
                                } catch (e1) {
                                    logToResults(`First pokedex path failed: ${e1.message}`, 'error', resultsContainer);

                                    try {
                                        // Then try the path relative to the root
                                        pokedexPath = './pokedex-151.json';
                                        response = await fetch(pokedexPath);
                                        if (!response.ok) {
                                            throw new Error(`HTTP error! status: ${response.status}`);
                                        }
                                    } catch (e2) {
                                        logToResults(`Second pokedex path failed: ${e2.message}`, 'error', resultsContainer);
                                        throw e2;
                                    }
                                }

                                logToResults(`Loading pokedex from ${pokedexPath}`, 'info', resultsContainer);
                                this.pokedexData = await response.json();

                                // Create mapping: evolution_chain_id -> base Pokemon
                                this.basePokemonByChain = {};
                                const chainSet = new Set();

                                for (const p of this.pokedexData) {
                                    if (!this.basePokemonByChain[p.evolution_chain_id]) {
                                        this.basePokemonByChain[p.evolution_chain_id] = p;
                                    }
                                    chainSet.add(p.evolution_chain_id);
                                }

                                this.chainsList = Array.from(chainSet).sort((a, b) => a - b);

                                logToResults(`Pokedex data loaded - pokemonCount: ${this.pokedexData.length}, chainsCount: ${this.chainsList.length}`, 'success', resultsContainer);

                                // Notify listeners that data is loaded
                                this.events.emit('pokedexDataLoaded', {
                                    pokedexData: this.pokedexData,
                                    chainsList: this.chainsList
                                });
                            } catch (e) {
                                logToResults(`Error in custom pokedex loader: ${e.message}`, 'error', resultsContainer);
                                throw e;
                            }
                        };

                        // Call our custom loader
                        await gameState.loadPokedexData();

                        // Restore the original method after loading
                        gameState.loadPokedexData = originalLoadPokedexData;

                        logToResults('Pokedex data loaded successfully', 'success', resultsContainer);
                    } catch (e) {
                        logToResults(`Error loading pokedex data: ${e.message}`, 'error', resultsContainer);
                    }
                }

                // Load type effectiveness data
                try {
                    // Check if we need to load the type effectiveness data
                    if (!window.typeEffectiveness) {
                        // Load type effectiveness data - try different paths
                        try {
                            // First try the local copy in the tests directory
                            const typeEffectivenessResponse = await fetch('./pokemon-types-battle.json');
                            if (!typeEffectivenessResponse.ok) {
                                throw new Error(`HTTP error! status: ${typeEffectivenessResponse.status}`);
                            }
                            window.typeEffectiveness = await typeEffectivenessResponse.json();
                            logToResults('Type effectiveness data loaded successfully from ./pokemon-types-battle.json', 'success', resultsContainer);

                            // Also set the global typeEffectivenessData variable used by battle-calc.js
                            window.typeEffectivenessData = window.typeEffectiveness;
                        } catch (e1) {
                            logToResults(`First attempt failed: ${e1.message}`, 'error', resultsContainer);

                            try {
                                // Then try the path relative to the root
                                const typeEffectivenessResponse2 = await fetch('../pokemon-types-battle.json');
                                if (!typeEffectivenessResponse2.ok) {
                                    throw new Error(`HTTP error! status: ${typeEffectivenessResponse2.status}`);
                                }
                                window.typeEffectiveness = await typeEffectivenessResponse2.json();
                                window.typeEffectivenessData = window.typeEffectiveness;
                                logToResults('Type effectiveness data loaded successfully from ../pokemon-types-battle.json', 'success', resultsContainer);
                            } catch (e2) {
                                logToResults(`Second attempt failed: ${e2.message}`, 'error', resultsContainer);

                                // Create a minimal hardcoded version as fallback
                                window.typeEffectiveness = {
                                    "Electric": {
                                        "super_effective": ["Water", "Flying"],
                                        "not_very_effective": ["Electric", "Grass", "Dragon"],
                                        "no_effect": ["Ground"]
                                    },
                                    "Fire": {
                                        "super_effective": ["Grass", "Ice", "Bug", "Steel"],
                                        "not_very_effective": ["Fire", "Water", "Rock", "Dragon"],
                                        "no_effect": []
                                    },
                                    "Normal": {
                                        "super_effective": [],
                                        "not_very_effective": ["Rock", "Steel"],
                                        "no_effect": ["Ghost"]
                                    }
                                };
                                logToResults('Using hardcoded fallback type effectiveness data', 'info', resultsContainer);
                            }
                        }
                    }
                } catch (e) {
                    logToResults(`Error loading type effectiveness data: ${e.message}`, 'error', resultsContainer);
                }

                // Make sure the battle calculation module is loaded
                try {
                    // Import the battle calculation module
                    await import('../services/battle-calc.js');
                    logToResults('Battle calculation module loaded successfully', 'success', resultsContainer);
                } catch (e) {
                    logToResults(`Error loading battle calculation module: ${e.message}`, 'error', resultsContainer);
                }
            } catch (e) {
                logToResults(`Error initializing test: ${e.message}`, 'error', document.getElementById('battle-test-results'));
            }
        }

        // Create a Pikachu that's just about to evolve (level 31, almost 32)
        function createEvolutionReadyPikachu() {
            // Find Pikachu in the pokedex (case-insensitive search)
            const pikachuData = gameState.pokedexData.find(p =>
                p.name.toLowerCase() === 'pikachu' ||
                (p.de && p.de.toLowerCase() === 'pikachu')
            );

            if (!pikachuData) {
                // Log the first few entries of pokedexData to debug
                const pokedexSample = gameState.pokedexData.slice(0, 5).map(p => p.name).join(', ');
                logToResults(`Error: Pikachu not found in pokedex data. Sample entries: ${pokedexSample}`, 'error', document.getElementById('battle-test-results'));
                return null;
            }

            // Log the found Pikachu data
            logToResults(`Found Pikachu in pokedex: ID=${pikachuData.id}, dex=${pikachuData.dex_number}, evolution_level=${pikachuData.evolution_level}`, 'success', document.getElementById('battle-test-results'));

            // Create a Pikachu at level 31
            const pikachu = new Pokemon(pikachuData.name, pikachuData.types[0], 31);

            // Set additional properties from pokedex data
            pikachu.name = pikachuData.de || 'Pikachu'; // Use German name if available
            pikachu.types = pikachuData.types;
            pikachu.dex_number = pikachuData.dex_number;
            pikachu.image_url = pikachuData.image_url;
            pikachu.image = pikachuData.image_url;
            pikachu.rarity = pikachuData.rarity || 'common';
            pikachu.evolution_level = pikachuData.evolution_level;
            pikachu.evolution_chain_id = pikachuData.evolution_chain_id;
            pikachu.base_species = pikachuData.base_species;

            // Set experience to be just below level 32
            // This is a placeholder - the actual value would depend on your experience curve
            pikachu.experience = 31000;

            logToResults(`Created Pikachu: name=${pikachu.name}, level=${pikachu.level}, dex=${pikachu.dex_number}, types=${pikachu.types.join('/')}`, 'info', document.getElementById('battle-test-results'));

            return pikachu;
        }

        // Battle test - Pikachu evolving to Raichu
        document.getElementById('test-battle-button').addEventListener('click', async function() {
            const resultsContainer = document.getElementById('battle-test-results');
            resultsContainer.innerHTML = '';

            try {
                await initializeTest();

                // Create a Pikachu that's ready to evolve
                const pikachu = createEvolutionReadyPikachu();
                if (!pikachu) return;

                logToResults(`Created test Pikachu at level ${pikachu.level}`, 'info', resultsContainer);

                // Create a wild Pokemon to battle against
                const wildPokemon = new Pokemon('charmander', 'fire', 30);
                wildPokemon.name = 'Charmander';
                wildPokemon.types = ['fire'];
                wildPokemon.dex_number = 4;
                wildPokemon.image_url = '../src/PokemonSprites/4.png';
                wildPokemon.image = '../src/PokemonSprites/4.png';

                // Open battle screen with enough XP to trigger evolution
                openBattleScreenWithCallback(
                    pikachu,
                    wildPokemon,
                    (battleResult) => {
                        logToResults('Battle completed!', 'success', resultsContainer);
                        logToResults(`XP gained: ${battleResult.experienceGained}`, 'info', resultsContainer);
                        logToResults(`Pikachu should have evolved to Raichu`, 'info', resultsContainer);
                    }
                );

                // Override the battle result to ensure player wins and gains enough XP
                setTimeout(() => {
                    try {
                        // Find the battle overlay
                        const battleOverlay = document.getElementById('battle-overlay');
                        if (!battleOverlay) {
                            logToResults('Battle overlay not found', 'error', resultsContainer);
                            return;
                        }

                        // Get the BattleScreen instance
                        const battleScreenElement = battleOverlay.querySelector('.battle-container');
                        if (!battleScreenElement) {
                            logToResults('Battle screen element not found', 'error', resultsContainer);
                            return;
                        }

                        // Find the BattleScreen instance
                        const battleScreens = Array.from(document.querySelectorAll('*')).filter(el => el.__component && el.__component.constructor.name === 'BattleScreen');
                        if (battleScreens.length === 0) {
                            logToResults('No BattleScreen instances found', 'error', resultsContainer);
                            return;
                        }

                        const battleScreen = battleScreens[0].__component;
                        if (!battleScreen) {
                            logToResults('BattleScreen component not found', 'error', resultsContainer);
                            return;
                        }

                        // Override the battle result
                        battleScreen.battleResult.playerWins = true;
                        battleScreen.battleResult.experienceGained = 2000; // Enough to level up
                        logToResults('Battle result overridden to ensure evolution', 'success', resultsContainer);

                        // Manually trigger the experience gain animation
                        setTimeout(() => {
                            try {
                                battleScreen.animateExperienceGain();
                                logToResults('Experience gain animation triggered', 'success', resultsContainer);
                            } catch (e) {
                                logToResults(`Error triggering experience gain animation: ${e.message}`, 'error', resultsContainer);
                            }
                        }, 1000);
                    } catch (e) {
                        logToResults(`Error overriding battle result: ${e.message}`, 'error', resultsContainer);
                    }
                }, 1000);

            } catch (e) {
                logToResults(`Error running battle test: ${e.message}`, 'error', resultsContainer);
                console.error(e);
            }
        });

        // Caught screen test - showing evolved Raichu
        document.getElementById('test-caught-button').addEventListener('click', async function() {
            const resultsContainer = document.getElementById('caught-test-results');
            resultsContainer.innerHTML = '';

            try {
                await initializeTest();

                // Find Raichu in the pokedex
                const raichuData = gameState.pokedexData.find(p =>
                    p.name.toLowerCase() === 'raichu' ||
                    (p.de && p.de.toLowerCase() === 'raichu')
                );

                if (!raichuData) {
                    logToResults('Error: Raichu not found in pokedex data', 'error', resultsContainer);
                    return;
                }

                logToResults(`Found Raichu in pokedex: ID=${raichuData.id}, dex=${raichuData.dex_number}`, 'success', resultsContainer);

                // Find Pikachu in the pokedex to get the base species
                const pikachuData = gameState.pokedexData.find(p =>
                    p.name.toLowerCase() === 'pikachu' ||
                    (p.de && p.de.toLowerCase() === 'pikachu')
                );

                // Create a Raichu (evolved from Pikachu)
                const raichu = new Pokemon(raichuData.name, raichuData.types[0], 32);

                // Set additional properties from pokedex data
                raichu.name = raichuData.de || 'Raichu'; // Use German name if available
                raichu.types = raichuData.types;
                raichu.dex_number = raichuData.dex_number;

                // Fix image paths for the test environment
                let imageUrl = raichuData.image_url;
                if (imageUrl && imageUrl.startsWith('./src/')) {
                    imageUrl = '../' + imageUrl.substring(2); // Convert './src/' to '../src/'
                }

                raichu.image_url = imageUrl;
                raichu.image = imageUrl;
                raichu.rarity = raichuData.rarity || 'common';
                raichu.base_species = pikachuData ? pikachuData.name : 'pikachu'; // It evolved from Pikachu

                logToResults(`Created test Raichu at level ${raichu.level}`, 'info', resultsContainer);

                // In the real app, the evolved Raichu would be in the team, not in the caught Pokemon list
                // We need to simulate this by creating a team with Raichu as the first Pokemon

                // Create a mock team with Raichu as the first Pokemon (buddy)
                const mockTeam = [raichu];

                // Override the loadTeam function to return our mock team
                const originalLoadTeam = window.loadTeam;
                window.loadTeam = async function() {
                    return mockTeam;
                };

                // Override the getCaughtPokemon function to return an empty array
                const originalGetCaughtPokemon = pokemonManager.getCaughtPokemon;
                pokemonManager.getCaughtPokemon = function() {
                    return [];
                };

                // Override the getTeamPokemon function to return our mock team
                const originalGetTeamPokemon = pokemonManager.getTeamPokemon;
                pokemonManager.getTeamPokemon = function() {
                    return mockTeam;
                };

                // Show the Pokemon caught screen with the evolved Raichu
                showPokemonCaughtScreen(raichu, true, () => {
                    // Restore original functions
                    window.loadTeam = originalLoadTeam;
                    pokemonManager.getCaughtPokemon = originalGetCaughtPokemon;
                    pokemonManager.getTeamPokemon = originalGetTeamPokemon;

                    logToResults('Caught screen closed', 'info', resultsContainer);
                });

                logToResults('Showing Raichu in the team section (as buddy)', 'info', resultsContainer);

                // Add a note about the evolution
                logToResults('This Raichu has just evolved from Pikachu', 'success', resultsContainer);

            } catch (e) {
                logToResults(`Error running caught screen test: ${e.message}`, 'error', resultsContainer);
                console.error(e);
            }
        });

        // Fix icon paths for the test environment
        function fixIconPaths() {
            // Override the icon paths to use the correct paths from the test directory
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                const element = originalCreateElement.call(document, tagName);

                if (tagName.toLowerCase() === 'img') {
                    const originalSetAttribute = element.setAttribute;
                    element.setAttribute = function(name, value) {
                        if (name === 'src' && value && typeof value === 'string') {
                            // Fix paths for icons
                            if (value.startsWith('./icons/') || value.startsWith('icons/')) {
                                value = value.replace('./icons/', '../icons/').replace('icons/', '../icons/');
                            }
                            // Fix paths for Pokemon sprites
                            if (value.startsWith('./src/PokemonSprites/') || value.startsWith('src/PokemonSprites/')) {
                                value = value.replace('./src/PokemonSprites/', '../src/PokemonSprites/').replace('src/PokemonSprites/', '../src/PokemonSprites/');
                            }
                        }
                        return originalSetAttribute.call(this, name, value);
                    };
                }

                return element;
            };

            // Also patch the fetch function to handle icon paths
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                if (typeof url === 'string') {
                    // Fix paths for icons
                    if (url.startsWith('./icons/') || url.startsWith('icons/')) {
                        url = url.replace('./icons/', '../icons/').replace('icons/', '../icons/');
                    }
                    // Fix paths for Pokemon sprites
                    if (url.startsWith('./src/PokemonSprites/') || url.startsWith('src/PokemonSprites/')) {
                        url = url.replace('./src/PokemonSprites/', '../src/PokemonSprites/').replace('src/PokemonSprites/', '../src/PokemonSprites/');
                    }
                }
                return originalFetch.call(window, url, options);
            };
        }

        // Initialize on page load
        window.addEventListener('DOMContentLoaded', () => {
            fixIconPaths();
            initializeTest();
        });
    </script>
</body>
</html>
