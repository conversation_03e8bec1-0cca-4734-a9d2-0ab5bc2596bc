// services/spawn-levels.js
// Service for determining the levels of spawning wild Pokemon

import { logger } from '../utils/logger.js';
import { pokemonManager } from './pokemon-manager.js';

/**
 * Calculate the average level of the player's team
 * @returns {Promise<number>} - The average team level (defaults to 5 if no team)
 */
export async function getAverageTeamLevel() {
  try {
    // Initialize the Pokemon manager if not already initialized
    await pokemonManager.initialize();

    // Get team Pokemon
    const teamPokemon = pokemonManager.getTeamPokemon();
    
    // If team is empty, return default level (5)
    if (!teamPokemon || teamPokemon.length === 0) {
      logger.debug('No Pokemon in team, using default level 5 for spawns');
      return 5;
    }

    // Calculate average level
    const totalLevel = teamPokemon.reduce((sum, pokemon) => sum + (pokemon.level || 1), 0);
    const averageLevel = Math.round(totalLevel / teamPokemon.length);
    
    logger.debug(`Average team level for spawns: ${averageLevel} (from ${teamPokemon.length} Pokemon)`);
    return averageLevel;
  } catch (e) {
    logger.error('Error calculating average team level:', e);
    // Default to level 5 if there's an error
    return 5;
  }
}

/**
 * Get a weighted deviation from the average level
 * Smaller deviations are more likely than larger ones
 * @param {number} maxDeviation - Maximum deviation from average level
 * @returns {number} - The deviation value (positive or negative)
 */
function getWeightedDeviation(maxDeviation = 20) {
  const deviations = [];

  // Create weighted deviations (smaller deviations have higher weights)
  for (let i = 1; i <= maxDeviation; i++) {
    const weight = 1 / i; // Weight decreases as deviation increases
    deviations.push({ value: i, weight });
    deviations.push({ value: -i, weight });
  }

  // Calculate total weight for normalization
  const totalWeight = deviations.reduce((sum, d) => sum + d.weight, 0);
  let rand = Math.random() * totalWeight;

  // Select a deviation based on weighted probability
  for (const d of deviations) {
    if (rand < d.weight) return d.value;
    rand -= d.weight;
  }

  return 0; // Fallback to no deviation
}

/**
 * Get a spawn level for a wild Pokemon based on the player's team
 * 30% chance of exact average team level
 * 70% chance of level within ±20 of average (weighted toward smaller deviations)
 * @returns {Promise<number>} - The spawn level (minimum 1)
 */
export async function getSpawnLevel() {
  try {
    // Get average team level
    const averageTeamLevel = await getAverageTeamLevel();
    
    // 30% chance: exact average level
    if (Math.random() < 0.3) {
      logger.debug(`Spawn level: ${averageTeamLevel} (exact match to team average)`);
      return averageTeamLevel;
    } 
    
    // 70% chance: deviate from average
    const deviation = getWeightedDeviation();
    let spawnLevel = averageTeamLevel + deviation;
    
    // Ensure minimum level of 1
    if (spawnLevel < 1) spawnLevel = 1;
    
    // Cap maximum level at 100
    if (spawnLevel > 100) spawnLevel = 100;
    
    logger.debug(`Spawn level: ${spawnLevel} (team average ${averageTeamLevel} with deviation ${deviation})`);
    return spawnLevel;
  } catch (e) {
    logger.error('Error determining spawn level:', e);
    // Default to a random level between 1-10 if there's an error
    const fallbackLevel = Math.floor(Math.random() * 10) + 1;
    logger.debug(`Using fallback spawn level: ${fallbackLevel}`);
    return fallbackLevel;
  }
}
