// tests/run-xp-test.js
// <PERSON><PERSON><PERSON> to run the XP consistency test from the terminal

import { testXpConsistency } from './xp-consistency-test.js';
import { gameState } from '../state/game-state.js';

// Run the test and display results
async function runTest() {
  console.log('=== POKEMON XP CONSISTENCY TEST ===');

  try {
    // Load pokedex data directly
    console.log('Loading pokedex data...');

    try {
      // In Node.js environment, we need to use a different approach to load the file
      const fs = await import('fs/promises');
      const path = await import('path');

      // Construct the path to the pokedex file
      const pokedexPath = path.join(process.cwd(), 'www', 'pokedex-151.json');

      // Read the file
      const pokedexData = JSON.parse(await fs.readFile(pokedexPath, 'utf8'));
      console.log(`Loaded ${pokedexData.length} Pokemon from pokedex data`);

      // Make it available globally
      global.pokedexData = pokedexData;
    } catch (error) {
      console.error('Error loading pokedex data directly:', error);
      console.log('Falling back to game state initialization');
    }

    // Initialize game state
    console.log('Initializing game state...');
    await gameState.initialize();
    console.log('Game data loaded successfully');

    console.log('Running test...');
    const results = await testXpConsistency();

    console.log('\nTest Results:');
    if (results.isConsistent) {
      console.log('✅ SUCCESS: XP and level are consistent across all screens and operations');
      if (results.didLevelUp) {
        console.log(`✅ LEVEL UP SUCCESSFUL: ${results.originalLevel} -> ${results.battleScreenLevel}`);
      } else {
        console.log(`⚠️ NOTE: Level up did not occur as expected. Still at level ${results.battleScreenLevel}`);
      }
    } else if (results.error) {
      console.log(`❌ ERROR: ${results.error}`);
    } else {
      console.log('❌ ERROR: Inconsistency detected');
    }

    console.log('\nTest Details:');
    console.log(`- Original XP: ${results.originalXp}`);
    console.log(`- XP Gained: ${results.xpGained}`);
    console.log(`- Expected New XP: ${results.expectedNewXp}`);

    console.log('\nXP Values Across Screens:');
    console.log(`- BattleScreen XP: ${results.battleScreenXp}`);
    console.log(`- Reloaded XP: ${results.reloadedXp}`);
    console.log(`- New Battle XP: ${results.newBattleXp}`);
    console.log(`- PokemonCaughtScreen XP: ${results.caughtScreenXp}`);

    console.log('\nLevel Values Across Screens:');
    console.log(`- Original Level: ${results.originalLevel}`);
    console.log(`- BattleScreen Level: ${results.battleScreenLevel}`);
    console.log(`- Reloaded Level: ${results.reloadedLevel}`);
    console.log(`- New Battle Level: ${results.newBattleLevel}`);
    console.log(`- PokemonCaughtScreen Level: ${results.caughtScreenLevel}`);

    if (!results.isConsistent && !results.error) {
      console.log('\nInconsistencies:');

      if (!results.isXpConsistent) {
        console.log('XP inconsistencies:');
        if (results.battleScreenXp !== results.reloadedXp) {
          console.log(`- BattleScreen vs Reload: ${results.battleScreenXp} ≠ ${results.reloadedXp}`);
        }
        if (results.reloadedXp !== results.newBattleXp) {
          console.log(`- Reload vs New Battle: ${results.reloadedXp} ≠ ${results.newBattleXp}`);
        }
        if (results.newBattleXp !== results.caughtScreenXp) {
          console.log(`- New Battle vs PokemonCaughtScreen: ${results.newBattleXp} ≠ ${results.caughtScreenXp}`);
        }
        console.log('');
      }

      if (!results.isLevelConsistent) {
        console.log('Level inconsistencies:');
        if (results.battleScreenLevel !== results.reloadedLevel) {
          console.log(`- BattleScreen vs Reload: ${results.battleScreenLevel} ≠ ${results.reloadedLevel}`);
        }
        if (results.reloadedLevel !== results.newBattleLevel) {
          console.log(`- Reload vs New Battle: ${results.reloadedLevel} ≠ ${results.newBattleLevel}`);
        }
        if (results.newBattleLevel !== results.caughtScreenLevel) {
          console.log(`- New Battle vs PokemonCaughtScreen: ${results.newBattleLevel} ≠ ${results.caughtScreenLevel}`);
        }
      }
    }

    console.log('\n=== TEST COMPLETED ===');
  } catch (error) {
    console.error('Error running test:', error);
  }
}

// Run the test
runTest();
