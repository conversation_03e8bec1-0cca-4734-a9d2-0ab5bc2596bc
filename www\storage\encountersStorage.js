// encountersStorage.js
// Module for managing Pokemon encounters in storage

import { config } from '../config.js';
import { storageService } from './storage-service.js';
import { logger } from '../utils/logger.js';

const STORAGE_KEY = config.storage.encountersKey;

/**
 * Load all stored encounters
 * @returns {Promise<Array<{ id: string, name: string, level: number, timestamp: number }>>}
 */
export async function loadEncounters() {
  try {
    return await storageService.get(STORAGE_KEY, []);
  } catch (e) {
    logger.error('Error loading encounters:', e);
    return [];
  }
}

/**
 * Save the provided list of encounters
 * @param {Array} encounters - The encounters to save
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function saveEncounters(encounters) {
  try {
    return await storageService.set(STORAGE_KEY, encounters);
  } catch (e) {
    logger.error('Error saving encounters:', e);
    return false;
  }
}

/**
 * Add a new encounter
 * @param {{ id: string, name: string, level: number }} pokemon - The Pokemon to add
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function addEncounter(pokemon) {
  try {
    const encounters = await loadEncounters();
    encounters.push({
      ...pokemon,
      timestamp: Date.now()
    });
    return await saveEncounters(encounters);
  } catch (e) {
    logger.error('Error adding encounter:', e);
    return false;
  }
}

/**
 * Get encounters for a specific Pokemon
 * @param {string} id - The Pokemon ID
 * @returns {Promise<Array<{ id: string, name: string, level: number, timestamp: number }>>}
 */
export async function getEncountersForPokemon(id) {
  try {
    const encounters = await loadEncounters();
    return encounters.filter(e => e.id === id);
  } catch (e) {
    logger.error('Error getting encounters for Pokemon:', e);
    return [];
  }
}

/**
 * Remove a specific encounter by ID
 * @param {string} id - The ID of the encounter to remove
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function removeEncounter(id) {
  try {
    const encounters = await loadEncounters();
    const index = encounters.findIndex(e => e.id === id);

    if (index === -1) {
      logger.warn(`Encounter with ID ${id} not found`);
      return false;
    }

    // Remove the encounter
    encounters.splice(index, 1);

    return await saveEncounters(encounters);
  } catch (e) {
    logger.error(`Error removing encounter with ID ${id}:`, e);
    return false;
  }
}

/**
 * Clear all encounters
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function clearEncounters() {
  try {
    return await storageService.set(STORAGE_KEY, []);
  } catch (e) {
    logger.error('Error clearing encounters:', e);
    return false;
  }
}
