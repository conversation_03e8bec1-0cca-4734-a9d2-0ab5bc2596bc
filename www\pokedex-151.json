[{"id": 1, "dex_number": 1, "name": "bulbasaur", "evolution_chain_id": 1, "base_species": "bulbasaur", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/1.png", "de": "Bisasam", "evolution_level": 16, "evolution_item": null, "rarity": "starter", "flying_offset": 0}, {"id": 2, "dex_number": 2, "name": "ivysaur", "evolution_chain_id": 1, "base_species": "bulbasaur", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/2.png", "de": "Bisaknosp", "evolution_level": 32, "evolution_item": null, "rarity": "starter"}, {"id": 3, "dex_number": 3, "name": "venusaur", "evolution_chain_id": 1, "base_species": "bulbasaur", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/3.png", "de": "Bisaflor", "evolution_level": null, "evolution_item": null, "rarity": "starter"}, {"id": 4, "dex_number": 4, "name": "charmander", "evolution_chain_id": 2, "base_species": "charmander", "types": ["fire"], "image_url": "./src/PokemonSprites/4.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 16, "evolution_item": null, "rarity": "starter"}, {"id": 5, "dex_number": 5, "name": "charmeleon", "evolution_chain_id": 2, "base_species": "charmander", "types": ["fire"], "image_url": "./src/PokemonSprites/5.png", "de": "Glutexo", "evolution_level": 36, "evolution_item": null, "rarity": "starter"}, {"id": 6, "dex_number": 6, "name": "charizard", "evolution_chain_id": 2, "base_species": "charmander", "types": ["fire", "flying"], "image_url": "./src/PokemonSprites/6.png", "de": "Glurak", "evolution_level": null, "evolution_item": null, "rarity": "starter", "flying_offset": -10}, {"id": 7, "dex_number": 7, "name": "squirtle", "evolution_chain_id": 3, "base_species": "squirtle", "types": ["water"], "image_url": "./src/PokemonSprites/7.png", "de": "<PERSON>higgy", "evolution_level": 16, "evolution_item": null, "rarity": "starter"}, {"id": 8, "dex_number": 8, "name": "wartortle", "evolution_chain_id": 3, "base_species": "squirtle", "types": ["water"], "image_url": "./src/PokemonSprites/8.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 36, "evolution_item": null, "rarity": "starter"}, {"id": 9, "dex_number": 9, "name": "blastoise", "evolution_chain_id": 3, "base_species": "squirtle", "types": ["water"], "image_url": "./src/PokemonSprites/9.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "starter"}, {"id": 10, "dex_number": 10, "name": "caterpie", "evolution_chain_id": 4, "base_species": "caterpie", "types": ["bug"], "image_url": "./src/PokemonSprites/10.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 7, "evolution_item": null, "rarity": "common"}, {"id": 11, "dex_number": 11, "name": "metapod", "evolution_chain_id": 4, "base_species": "caterpie", "types": ["bug"], "image_url": "./src/PokemonSprites/11.png", "de": "Safcon", "evolution_level": 10, "evolution_item": null, "rarity": "common"}, {"id": 12, "dex_number": 12, "name": "butterfree", "evolution_chain_id": 4, "base_species": "caterpie", "types": ["bug", "flying"], "image_url": "./src/PokemonSprites/12.png", "de": "Smettbo", "evolution_level": null, "evolution_item": null, "rarity": "common", "flying_offset": -10}, {"id": 13, "dex_number": 13, "name": "weedle", "evolution_chain_id": 5, "base_species": "weedle", "types": ["bug", "poison"], "image_url": "./src/PokemonSprites/13.png", "de": "Hornliu", "evolution_level": 7, "evolution_item": null, "rarity": "common"}, {"id": 14, "dex_number": 14, "name": "kakuna", "evolution_chain_id": 5, "base_species": "weedle", "types": ["bug", "poison"], "image_url": "./src/PokemonSprites/14.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 10, "evolution_item": null, "rarity": "common"}, {"id": 15, "dex_number": 15, "name": "beedrill", "evolution_chain_id": 5, "base_species": "weedle", "types": ["bug", "poison"], "image_url": "./src/PokemonSprites/15.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 16, "dex_number": 16, "name": "pidgey", "evolution_chain_id": 6, "base_species": "pidgey", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/16.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 18, "evolution_item": null, "rarity": "common", "flying_offset": -10}, {"id": 17, "dex_number": 17, "name": "pidgeotto", "evolution_chain_id": 6, "base_species": "pidgey", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/17.png", "de": "Tauboga", "evolution_level": 36, "evolution_item": null, "rarity": "common", "flying_offset": -15}, {"id": 18, "dex_number": 18, "name": "pidgeot", "evolution_chain_id": 6, "base_species": "pidgey", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/18.png", "de": "Tau<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common", "flying_offset": -15}, {"id": 19, "dex_number": 19, "name": "rattata", "evolution_chain_id": 7, "base_species": "rattata", "types": ["normal"], "image_url": "./src/PokemonSprites/19.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": 20, "evolution_item": null, "rarity": "common"}, {"id": 20, "dex_number": 20, "name": "raticate", "evolution_chain_id": 7, "base_species": "rattata", "types": ["normal"], "image_url": "./src/PokemonSprites/20.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 21, "dex_number": 21, "name": "spearow", "evolution_chain_id": 8, "base_species": "spearow", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/21.png", "de": "<PERSON><PERSON>ak", "evolution_level": 20, "evolution_item": null, "rarity": "common", "flying_offset": -10}, {"id": 22, "dex_number": 22, "name": "fearow", "evolution_chain_id": 8, "base_species": "spearow", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/22.png", "de": "Ibitak", "evolution_level": null, "evolution_item": null, "rarity": "common", "flying_offset": -20}, {"id": 23, "dex_number": 23, "name": "ekans", "evolution_chain_id": 9, "base_species": "ekans", "types": ["poison"], "image_url": "./src/PokemonSprites/23.png", "de": "Rettan", "evolution_level": 22, "evolution_item": null, "rarity": "common"}, {"id": 24, "dex_number": 24, "name": "arbok", "evolution_chain_id": 9, "base_species": "ekans", "types": ["poison"], "image_url": "./src/PokemonSprites/24.png", "de": "<PERSON>rbok", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 25, "dex_number": 25, "name": "pikachu", "evolution_chain_id": 10, "base_species": "pichu", "types": ["electric"], "image_url": "./src/PokemonSprites/25.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 32, "evolution_item": "thunder-stone", "rarity": "common"}, {"id": 26, "dex_number": 26, "name": "r<PERSON><PERSON>", "evolution_chain_id": 10, "base_species": "pichu", "types": ["electric"], "image_url": "./src/PokemonSprites/26.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 27, "dex_number": 27, "name": "sandshrew", "evolution_chain_id": 11, "base_species": "sandshrew", "types": ["ground"], "image_url": "./src/PokemonSprites/27.png", "de": "<PERSON><PERSON>", "evolution_level": 22, "evolution_item": null, "rarity": "common"}, {"id": 28, "dex_number": 28, "name": "sandslash", "evolution_chain_id": 11, "base_species": "sandshrew", "types": ["ground"], "image_url": "./src/PokemonSprites/28.png", "de": "Sandamer", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 29, "dex_number": 29, "name": "nidoran-f", "evolution_chain_id": 12, "base_species": "nidoran-f", "types": ["poison"], "image_url": "./src/PokemonSprites/29.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 16, "evolution_item": null, "rarity": "common"}, {"id": 30, "dex_number": 30, "name": "nidorina", "evolution_chain_id": 12, "base_species": "nidoran-f", "types": ["poison"], "image_url": "./src/PokemonSprites/30.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 32, "evolution_item": "moon-stone", "rarity": "common"}, {"id": 31, "dex_number": 31, "name": "nidoqueen", "evolution_chain_id": 12, "base_species": "nidoran-f", "types": ["poison", "ground"], "image_url": "./src/PokemonSprites/31.png", "de": "Nido<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 32, "dex_number": 32, "name": "nidoran-m", "evolution_chain_id": 13, "base_species": "nidoran-m", "types": ["poison"], "image_url": "./src/PokemonSprites/32.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 16, "evolution_item": null, "rarity": "common"}, {"id": 33, "dex_number": 33, "name": "<PERSON><PERSON><PERSON>", "evolution_chain_id": 13, "base_species": "nidoran-m", "types": ["poison"], "image_url": "./src/PokemonSprites/33.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 32, "evolution_item": "moon-stone", "rarity": "common"}, {"id": 34, "dex_number": 34, "name": "nidoking", "evolution_chain_id": 13, "base_species": "nidoran-m", "types": ["poison", "ground"], "image_url": "./src/PokemonSprites/34.png", "de": "Nidoking", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 35, "dex_number": 35, "name": "cle<PERSON>y", "evolution_chain_id": 14, "base_species": "cleffa", "types": ["fairy"], "image_url": "./src/PokemonSprites/35.png", "de": "<PERSON><PERSON>", "evolution_level": 32, "evolution_item": "moon-stone", "rarity": "common"}, {"id": 36, "dex_number": 36, "name": "clefable", "evolution_chain_id": 14, "base_species": "cleffa", "types": ["fairy"], "image_url": "./src/PokemonSprites/36.png", "de": "Pixi", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 37, "dex_number": 37, "name": "vulpix", "evolution_chain_id": 15, "base_species": "vulpix", "types": ["fire"], "image_url": "./src/PokemonSprites/37.png", "de": "Vulpix", "evolution_level": 32, "evolution_item": "fire-stone", "rarity": "common"}, {"id": 38, "dex_number": 38, "name": "ninetales", "evolution_chain_id": 15, "base_species": "vulpix", "types": ["fire"], "image_url": "./src/PokemonSprites/38.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 39, "dex_number": 39, "name": "jigglypuff", "evolution_chain_id": 16, "base_species": "igglybuff", "types": ["normal", "fairy"], "image_url": "./src/PokemonSprites/39.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": 32, "evolution_item": "moon-stone", "rarity": "common"}, {"id": 40, "dex_number": 40, "name": "wigglytuff", "evolution_chain_id": 16, "base_species": "igglybuff", "types": ["normal", "fairy"], "image_url": "./src/PokemonSprites/40.png", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 41, "dex_number": 41, "name": "zubat", "evolution_chain_id": 17, "base_species": "zubat", "types": ["poison", "flying"], "image_url": "./src/PokemonSprites/41.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 22, "evolution_item": null, "rarity": "common", "flying_offset": -20}, {"id": 42, "dex_number": 42, "name": "golbat", "evolution_chain_id": 17, "base_species": "zubat", "types": ["poison", "flying"], "image_url": "./src/PokemonSprites/42.png", "de": "Golbat", "evolution_level": null, "evolution_item": null, "rarity": "common", "flying_offset": -20}, {"id": 43, "dex_number": 43, "name": "oddish", "evolution_chain_id": 18, "base_species": "oddish", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/43.png", "de": "Myra<PERSON><PERSON>", "evolution_level": 21, "evolution_item": null, "rarity": "common"}, {"id": 44, "dex_number": 44, "name": "gloom", "evolution_chain_id": 18, "base_species": "oddish", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/44.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 42, "evolution_item": "leaf-stone", "rarity": "common"}, {"id": 45, "dex_number": 45, "name": "vileplume", "evolution_chain_id": 18, "base_species": "oddish", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/45.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 46, "dex_number": 46, "name": "paras", "evolution_chain_id": 19, "base_species": "paras", "types": ["bug", "grass"], "image_url": "./src/PokemonSprites/46.png", "de": "Paras", "evolution_level": 24, "evolution_item": null, "rarity": "common"}, {"id": 47, "dex_number": 47, "name": "parasect", "evolution_chain_id": 19, "base_species": "paras", "types": ["bug", "grass"], "image_url": "./src/PokemonSprites/47.png", "de": "Parasek", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 48, "dex_number": 48, "name": "venonat", "evolution_chain_id": 20, "base_species": "venonat", "types": ["bug", "poison"], "image_url": "./src/PokemonSprites/48.png", "de": "Bluzuk", "evolution_level": 31, "evolution_item": null, "rarity": "common"}, {"id": 49, "dex_number": 49, "name": "venomoth", "evolution_chain_id": 20, "base_species": "venonat", "types": ["bug", "poison"], "image_url": "./src/PokemonSprites/49.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 50, "dex_number": 50, "name": "<PERSON><PERSON>", "evolution_chain_id": 21, "base_species": "<PERSON><PERSON>", "types": ["ground"], "image_url": "./src/PokemonSprites/50.png", "de": "<PERSON>g<PERSON>", "evolution_level": 26, "evolution_item": null, "rarity": "common"}, {"id": 51, "dex_number": 51, "name": "<PERSON><PERSON>o", "evolution_chain_id": 21, "base_species": "<PERSON><PERSON>", "types": ["ground"], "image_url": "./src/PokemonSprites/51.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 52, "dex_number": 52, "name": "meowth", "evolution_chain_id": 22, "base_species": "meowth", "types": ["normal"], "image_url": "./src/PokemonSprites/52.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 28, "evolution_item": null, "rarity": "common"}, {"id": 53, "dex_number": 53, "name": "persian", "evolution_chain_id": 22, "base_species": "meowth", "types": ["normal"], "image_url": "./src/PokemonSprites/53.png", "de": "Snobilikat", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 54, "dex_number": 54, "name": "psyduck", "evolution_chain_id": 23, "base_species": "psyduck", "types": ["water"], "image_url": "./src/PokemonSprites/54.png", "de": "<PERSON><PERSON>", "evolution_level": 33, "evolution_item": null, "rarity": "common"}, {"id": 55, "dex_number": 55, "name": "golduck", "evolution_chain_id": 23, "base_species": "psyduck", "types": ["water"], "image_url": "./src/PokemonSprites/55.png", "de": "Entoron", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 56, "dex_number": 56, "name": "mankey", "evolution_chain_id": 24, "base_species": "mankey", "types": ["fighting"], "image_url": "./src/PokemonSprites/56.png", "de": "<PERSON><PERSON>", "evolution_level": 28, "evolution_item": null, "rarity": "common"}, {"id": 57, "dex_number": 57, "name": "primeape", "evolution_chain_id": 24, "base_species": "mankey", "types": ["fighting"], "image_url": "./src/PokemonSprites/57.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 58, "dex_number": 58, "name": "growlithe", "evolution_chain_id": 25, "base_species": "growlithe", "types": ["fire"], "image_url": "./src/PokemonSprites/58.png", "de": "Fukano", "evolution_level": 32, "evolution_item": "fire-stone", "rarity": "common"}, {"id": 59, "dex_number": 59, "name": "arcanine", "evolution_chain_id": 25, "base_species": "growlithe", "types": ["fire"], "image_url": "./src/PokemonSprites/59.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 60, "dex_number": 60, "name": "poliwag", "evolution_chain_id": 26, "base_species": "poliwag", "types": ["water"], "image_url": "./src/PokemonSprites/60.png", "de": "Quapsel", "evolution_level": 25, "evolution_item": null, "rarity": "common"}, {"id": 61, "dex_number": 61, "name": "poliwhirl", "evolution_chain_id": 26, "base_species": "poliwag", "types": ["water"], "image_url": "./src/PokemonSprites/61.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": 50, "evolution_item": "water-stone", "rarity": "common"}, {"id": 62, "dex_number": 62, "name": "poliwrath", "evolution_chain_id": 26, "base_species": "poliwag", "types": ["water", "fighting"], "image_url": "./src/PokemonSprites/62.png", "de": "Quappo", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 63, "dex_number": 63, "name": "abra", "evolution_chain_id": 27, "base_species": "abra", "types": ["psychic"], "image_url": "./src/PokemonSprites/63.png", "de": "Abra", "evolution_level": 16, "evolution_item": null, "rarity": "common"}, {"id": 64, "dex_number": 64, "name": "kadabra", "evolution_chain_id": 27, "base_species": "abra", "types": ["psychic"], "image_url": "./src/PokemonSprites/64.png", "de": "Kadabra", "evolution_level": 32, "evolution_item": null, "rarity": "common"}, {"id": 65, "dex_number": 65, "name": "<PERSON><PERSON><PERSON>", "evolution_chain_id": 27, "base_species": "abra", "types": ["psychic"], "image_url": "./src/PokemonSprites/65.png", "de": "Simsala", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 66, "dex_number": 66, "name": "machop", "evolution_chain_id": 28, "base_species": "machop", "types": ["fighting"], "image_url": "./src/PokemonSprites/66.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 28, "evolution_item": null, "rarity": "common"}, {"id": 67, "dex_number": 67, "name": "machoke", "evolution_chain_id": 28, "base_species": "machop", "types": ["fighting"], "image_url": "./src/PokemonSprites/67.png", "de": "Maschock", "evolution_level": 56, "evolution_item": null, "rarity": "common"}, {"id": 68, "dex_number": 68, "name": "machamp", "evolution_chain_id": 28, "base_species": "machop", "types": ["fighting"], "image_url": "./src/PokemonSprites/68.png", "de": "Machomei", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 69, "dex_number": 69, "name": "bellsprout", "evolution_chain_id": 29, "base_species": "bellsprout", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/69.png", "de": "Knofensa", "evolution_level": 21, "evolution_item": null, "rarity": "common"}, {"id": 70, "dex_number": 70, "name": "weepinbell", "evolution_chain_id": 29, "base_species": "bellsprout", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/70.png", "de": "Ultrigaria", "evolution_level": 42, "evolution_item": "leaf-stone", "rarity": "common"}, {"id": 71, "dex_number": 71, "name": "victreebel", "evolution_chain_id": 29, "base_species": "bellsprout", "types": ["grass", "poison"], "image_url": "./src/PokemonSprites/71.png", "de": "Sarzenia", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 72, "dex_number": 72, "name": "tentacool", "evolution_chain_id": 30, "base_species": "tentacool", "types": ["water", "poison"], "image_url": "./src/PokemonSprites/72.png", "de": "Tentacha", "evolution_level": 30, "evolution_item": null, "rarity": "common"}, {"id": 73, "dex_number": 73, "name": "tentacruel", "evolution_chain_id": 30, "base_species": "tentacool", "types": ["water", "poison"], "image_url": "./src/PokemonSprites/73.png", "de": "Tentoxa", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 74, "dex_number": 74, "name": "geodude", "evolution_chain_id": 31, "base_species": "geodude", "types": ["rock", "ground"], "image_url": "./src/PokemonSprites/74.png", "de": "<PERSON><PERSON>", "evolution_level": 25, "evolution_item": null, "rarity": "common"}, {"id": 75, "dex_number": 75, "name": "graveler", "evolution_chain_id": 31, "base_species": "geodude", "types": ["rock", "ground"], "image_url": "./src/PokemonSprites/75.png", "de": "Georok", "evolution_level": 50, "evolution_item": null, "rarity": "common"}, {"id": 76, "dex_number": 76, "name": "golem", "evolution_chain_id": 31, "base_species": "geodude", "types": ["rock", "ground"], "image_url": "./src/PokemonSprites/76.png", "de": "Geowaz", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 77, "dex_number": 77, "name": "ponyta", "evolution_chain_id": 32, "base_species": "ponyta", "types": ["fire"], "image_url": "./src/PokemonSprites/77.png", "de": "Poni<PERSON>", "evolution_level": 40, "evolution_item": null, "rarity": "common"}, {"id": 78, "dex_number": 78, "name": "rapidash", "evolution_chain_id": 32, "base_species": "ponyta", "types": ["fire"], "image_url": "./src/PokemonSprites/78.png", "de": "Gallopa", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 79, "dex_number": 79, "name": "slowpoke", "evolution_chain_id": 33, "base_species": "slowpoke", "types": ["water", "psychic"], "image_url": "./src/PokemonSprites/79.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 37, "evolution_item": null, "rarity": "common"}, {"id": 80, "dex_number": 80, "name": "slowbro", "evolution_chain_id": 33, "base_species": "slowpoke", "types": ["water", "psychic"], "image_url": "./src/PokemonSprites/80.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 81, "dex_number": 81, "name": "magnemite", "evolution_chain_id": 34, "base_species": "magnemite", "types": ["electric", "steel"], "image_url": "./src/PokemonSprites/81.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": 30, "evolution_item": null, "rarity": "common"}, {"id": 82, "dex_number": 82, "name": "magneton", "evolution_chain_id": 34, "base_species": "magnemite", "types": ["electric", "steel"], "image_url": "./src/PokemonSprites/82.png", "de": "Magneton", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 83, "dex_number": 83, "name": "farfetchd", "evolution_chain_id": 35, "base_species": "farfetchd", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/83.png", "de": "Porenta", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 84, "dex_number": 84, "name": "doduo", "evolution_chain_id": 36, "base_species": "doduo", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/84.png", "de": "Dodu", "evolution_level": 31, "evolution_item": null, "rarity": "common"}, {"id": 85, "dex_number": 85, "name": "dodrio", "evolution_chain_id": 36, "base_species": "doduo", "types": ["normal", "flying"], "image_url": "./src/PokemonSprites/85.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 86, "dex_number": 86, "name": "seel", "evolution_chain_id": 37, "base_species": "seel", "types": ["water"], "image_url": "./src/PokemonSprites/86.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 34, "evolution_item": null, "rarity": "common"}, {"id": 87, "dex_number": 87, "name": "dewgong", "evolution_chain_id": 37, "base_species": "seel", "types": ["water", "ice"], "image_url": "./src/PokemonSprites/87.png", "de": "Jugong", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 88, "dex_number": 88, "name": "grimer", "evolution_chain_id": 38, "base_species": "grimer", "types": ["poison"], "image_url": "./src/PokemonSprites/88.png", "de": "S<PERSON><PERSON>", "evolution_level": 38, "evolution_item": null, "rarity": "common"}, {"id": 89, "dex_number": 89, "name": "muk", "evolution_chain_id": 38, "base_species": "grimer", "types": ["poison"], "image_url": "./src/PokemonSprites/89.png", "de": "Sleimok", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 90, "dex_number": 90, "name": "shellder", "evolution_chain_id": 39, "base_species": "shellder", "types": ["water"], "image_url": "./src/PokemonSprites/90.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 32, "evolution_item": "water-stone", "rarity": "common"}, {"id": 91, "dex_number": 91, "name": "cloyster", "evolution_chain_id": 39, "base_species": "shellder", "types": ["water", "ice"], "image_url": "./src/PokemonSprites/91.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 92, "dex_number": 92, "name": "gastly", "evolution_chain_id": 40, "base_species": "gastly", "types": ["ghost", "poison"], "image_url": "./src/PokemonSprites/92.png", "de": "Nebulak", "evolution_level": 25, "evolution_item": null, "rarity": "common"}, {"id": 93, "dex_number": 93, "name": "haunter", "evolution_chain_id": 40, "base_species": "gastly", "types": ["ghost", "poison"], "image_url": "./src/PokemonSprites/93.png", "de": "Alpollo", "evolution_level": 50, "evolution_item": null, "rarity": "common"}, {"id": 94, "dex_number": 94, "name": "gengar", "evolution_chain_id": 40, "base_species": "gastly", "types": ["ghost", "poison"], "image_url": "./src/PokemonSprites/94.png", "de": "Gengar", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 95, "dex_number": 95, "name": "onix", "evolution_chain_id": 41, "base_species": "onix", "types": ["rock", "ground"], "image_url": "./src/PokemonSprites/95.png", "de": "Onix", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 96, "dex_number": 96, "name": "drowzee", "evolution_chain_id": 42, "base_species": "drowzee", "types": ["psychic"], "image_url": "./src/PokemonSprites/96.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": 26, "evolution_item": null, "rarity": "common"}, {"id": 97, "dex_number": 97, "name": "hypno", "evolution_chain_id": 42, "base_species": "drowzee", "types": ["psychic"], "image_url": "./src/PokemonSprites/97.png", "de": "Hypno", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 98, "dex_number": 98, "name": "krabby", "evolution_chain_id": 43, "base_species": "krabby", "types": ["water"], "image_url": "./src/PokemonSprites/98.png", "de": "K<PERSON><PERSON>", "evolution_level": 28, "evolution_item": null, "rarity": "common"}, {"id": 99, "dex_number": 99, "name": "kingler", "evolution_chain_id": 43, "base_species": "krabby", "types": ["water"], "image_url": "./src/PokemonSprites/99.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 100, "dex_number": 100, "name": "voltorb", "evolution_chain_id": 44, "base_species": "voltorb", "types": ["electric"], "image_url": "./src/PokemonSprites/100.png", "de": "Voltobal", "evolution_level": 30, "evolution_item": null, "rarity": "common"}, {"id": 101, "dex_number": 101, "name": "electrode", "evolution_chain_id": 44, "base_species": "voltorb", "types": ["electric"], "image_url": "./src/PokemonSprites/101.png", "de": "Lektrobal", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 102, "dex_number": 102, "name": "exeggcute", "evolution_chain_id": 45, "base_species": "exeggcute", "types": ["grass", "psychic"], "image_url": "./src/PokemonSprites/102.png", "de": "<PERSON><PERSON>", "evolution_level": 32, "evolution_item": "leaf-stone", "rarity": "common"}, {"id": 103, "dex_number": 103, "name": "exeggutor", "evolution_chain_id": 45, "base_species": "exeggcute", "types": ["grass", "psychic"], "image_url": "./src/PokemonSprites/103.png", "de": "Kokowei", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 104, "dex_number": 104, "name": "cubone", "evolution_chain_id": 46, "base_species": "cubone", "types": ["ground"], "image_url": "./src/PokemonSprites/104.png", "de": "Tragoss<PERSON>", "evolution_level": 28, "evolution_item": null, "rarity": "common"}, {"id": 105, "dex_number": 105, "name": "marowak", "evolution_chain_id": 46, "base_species": "cubone", "types": ["ground"], "image_url": "./src/PokemonSprites/105.png", "de": "Knogga", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 106, "dex_number": 106, "name": "<PERSON><PERSON><PERSON>", "evolution_chain_id": 47, "base_species": "tyrogue", "types": ["fighting"], "image_url": "./src/PokemonSprites/106.png", "de": "Kicklee", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 107, "dex_number": 107, "name": "hitmonchan", "evolution_chain_id": 47, "base_species": "tyrogue", "types": ["fighting"], "image_url": "./src/PokemonSprites/107.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 108, "dex_number": 108, "name": "lickitung", "evolution_chain_id": 48, "base_species": "lickitung", "types": ["normal"], "image_url": "./src/PokemonSprites/108.png", "de": "<PERSON><PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 109, "dex_number": 109, "name": "koffing", "evolution_chain_id": 49, "base_species": "koffing", "types": ["poison"], "image_url": "./src/PokemonSprites/109.png", "de": "Smogon", "evolution_level": 35, "evolution_item": null, "rarity": "common"}, {"id": 110, "dex_number": 110, "name": "weezing", "evolution_chain_id": 49, "base_species": "koffing", "types": ["poison"], "image_url": "./src/PokemonSprites/110.png", "de": "Smogmog", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 111, "dex_number": 111, "name": "rhyhorn", "evolution_chain_id": 50, "base_species": "rhyhorn", "types": ["ground", "rock"], "image_url": "./src/PokemonSprites/111.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 42, "evolution_item": null, "rarity": "common"}, {"id": 112, "dex_number": 112, "name": "rhydon", "evolution_chain_id": 50, "base_species": "rhyhorn", "types": ["ground", "rock"], "image_url": "./src/PokemonSprites/112.png", "de": "Rizeros", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 113, "dex_number": 113, "name": "chansey", "evolution_chain_id": 51, "base_species": "happiny", "types": ["normal"], "image_url": "./src/PokemonSprites/113.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 114, "dex_number": 114, "name": "tangela", "evolution_chain_id": 52, "base_species": "tangela", "types": ["grass"], "image_url": "./src/PokemonSprites/114.png", "de": "Tangela", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 115, "dex_number": 115, "name": "kangaskhan", "evolution_chain_id": 53, "base_species": "kangaskhan", "types": ["normal"], "image_url": "./src/PokemonSprites/115.png", "de": "Kangama", "evolution_level": null, "evolution_item": null, "rarity": "scarce"}, {"id": 116, "dex_number": 116, "name": "<PERSON>a", "evolution_chain_id": 54, "base_species": "<PERSON>a", "types": ["water"], "image_url": "./src/PokemonSprites/116.png", "de": "Seeper", "evolution_level": 32, "evolution_item": null, "rarity": "common"}, {"id": 117, "dex_number": 117, "name": "seadra", "evolution_chain_id": 54, "base_species": "<PERSON>a", "types": ["water"], "image_url": "./src/PokemonSprites/117.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 118, "dex_number": 118, "name": "goldeen", "evolution_chain_id": 55, "base_species": "goldeen", "types": ["water"], "image_url": "./src/PokemonSprites/118.png", "de": "<PERSON><PERSON>", "evolution_level": 33, "evolution_item": null, "rarity": "common"}, {"id": 119, "dex_number": 119, "name": "seaking", "evolution_chain_id": 55, "base_species": "goldeen", "types": ["water"], "image_url": "./src/PokemonSprites/119.png", "de": "Golking", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 120, "dex_number": 120, "name": "staryu", "evolution_chain_id": 56, "base_species": "staryu", "types": ["water"], "image_url": "./src/PokemonSprites/120.png", "de": "Stern<PERSON>", "evolution_level": 32, "evolution_item": "water-stone", "rarity": "common"}, {"id": 121, "dex_number": 121, "name": "starmie", "evolution_chain_id": 56, "base_species": "staryu", "types": ["water", "psychic"], "image_url": "./src/PokemonSprites/121.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 122, "dex_number": 122, "name": "mr-mime", "evolution_chain_id": 57, "base_species": "mime-jr", "types": ["psychic", "fairy"], "image_url": "./src/PokemonSprites/122.png", "de": "Pantimos", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 123, "dex_number": 123, "name": "scyther", "evolution_chain_id": 58, "base_species": "scyther", "types": ["bug", "flying"], "image_url": "./src/PokemonSprites/123.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "scarce"}, {"id": 124, "dex_number": 124, "name": "jynx", "evolution_chain_id": 59, "base_species": "smoochum", "types": ["ice", "psychic"], "image_url": "./src/PokemonSprites/124.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 125, "dex_number": 125, "name": "electabuzz", "evolution_chain_id": 60, "base_species": "elekid", "types": ["electric"], "image_url": "./src/PokemonSprites/125.png", "de": "Elektek", "evolution_level": null, "evolution_item": null, "rarity": "scarce"}, {"id": 126, "dex_number": 126, "name": "magmar", "evolution_chain_id": 61, "base_species": "magby", "types": ["fire"], "image_url": "./src/PokemonSprites/126.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "scarce"}, {"id": 127, "dex_number": 127, "name": "pinsir", "evolution_chain_id": 62, "base_species": "pinsir", "types": ["bug"], "image_url": "./src/PokemonSprites/127.png", "de": "Pinsir", "evolution_level": null, "evolution_item": null, "rarity": "scarce"}, {"id": 128, "dex_number": 128, "name": "tauros", "evolution_chain_id": 63, "base_species": "tauros", "types": ["normal"], "image_url": "./src/PokemonSprites/128.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "scarce"}, {"id": 129, "dex_number": 129, "name": "magi<PERSON><PERSON>", "evolution_chain_id": 64, "base_species": "magi<PERSON><PERSON>", "types": ["water"], "image_url": "./src/PokemonSprites/129.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 20, "evolution_item": null, "rarity": "common"}, {"id": 130, "dex_number": 130, "name": "gyarados", "evolution_chain_id": 64, "base_species": "magi<PERSON><PERSON>", "types": ["water", "flying"], "image_url": "./src/PokemonSprites/130.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 131, "dex_number": 131, "name": "<PERSON>ras", "evolution_chain_id": 65, "base_species": "<PERSON>ras", "types": ["water", "ice"], "image_url": "./src/PokemonSprites/131.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 132, "dex_number": 132, "name": "ditto", "evolution_chain_id": 66, "base_species": "ditto", "types": ["normal"], "image_url": "./src/PokemonSprites/132.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "common"}, {"id": 133, "dex_number": 133, "name": "eevee", "evolution_chain_id": 67, "base_species": "eevee", "types": ["normal"], "image_url": "./src/PokemonSprites/133.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": ["water-stone", "thunder-stone", "fire-stone"], "rarity": "scarce"}, {"id": 134, "dex_number": 134, "name": "vaporeon", "evolution_chain_id": 67, "base_species": "eevee", "types": ["water"], "image_url": "./src/PokemonSprites/134.png", "de": "Aquana", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 135, "dex_number": 135, "name": "jolteon", "evolution_chain_id": 67, "base_species": "eevee", "types": ["electric"], "image_url": "./src/PokemonSprites/135.png", "de": "Blitza", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 136, "dex_number": 136, "name": "flareon", "evolution_chain_id": 67, "base_species": "eevee", "types": ["fire"], "image_url": "./src/PokemonSprites/136.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 137, "dex_number": 137, "name": "porygon", "evolution_chain_id": 68, "base_species": "porygon", "types": ["normal"], "image_url": "./src/PokemonSprites/137.png", "de": "Porygon", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 138, "dex_number": 138, "name": "omanyte", "evolution_chain_id": 69, "base_species": "omanyte", "types": ["rock", "water"], "image_url": "./src/PokemonSprites/138.png", "de": "Amonitas", "evolution_level": 40, "evolution_item": null, "rarity": "rare"}, {"id": 139, "dex_number": 139, "name": "omastar", "evolution_chain_id": 69, "base_species": "omanyte", "types": ["rock", "water"], "image_url": "./src/PokemonSprites/139.png", "de": "<PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 140, "dex_number": 140, "name": "kabuto", "evolution_chain_id": 70, "base_species": "kabuto", "types": ["rock", "water"], "image_url": "./src/PokemonSprites/140.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 40, "evolution_item": null, "rarity": "rare"}, {"id": 141, "dex_number": 141, "name": "kabutops", "evolution_chain_id": 70, "base_species": "kabuto", "types": ["rock", "water"], "image_url": "./src/PokemonSprites/141.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 142, "dex_number": 142, "name": "aerodactyl", "evolution_chain_id": 71, "base_species": "aerodactyl", "types": ["rock", "flying"], "image_url": "./src/PokemonSprites/142.png", "de": "Aerodactyl", "evolution_level": null, "evolution_item": null, "rarity": "rare", "flying_offset": -20}, {"id": 143, "dex_number": 143, "name": "snorlax", "evolution_chain_id": 72, "base_species": "munchlax", "types": ["normal"], "image_url": "./src/PokemonSprites/143.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "rare"}, {"id": 144, "dex_number": 144, "name": "articuno", "evolution_chain_id": 73, "base_species": "articuno", "types": ["ice", "flying"], "image_url": "./src/PokemonSprites/144.png", "de": "Arktos", "evolution_level": null, "evolution_item": null, "rarity": "legendary", "flying_offset": -15}, {"id": 145, "dex_number": 145, "name": "zapdos", "evolution_chain_id": 74, "base_species": "zapdos", "types": ["electric", "flying"], "image_url": "./src/PokemonSprites/145.png", "de": "Zapdos", "evolution_level": null, "evolution_item": null, "rarity": "legendary", "flying_offset": -15}, {"id": 146, "dex_number": 146, "name": "moltres", "evolution_chain_id": 75, "base_species": "moltres", "types": ["fire", "flying"], "image_url": "./src/PokemonSprites/146.png", "de": "Lava<PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "legendary", "flying_offset": -15}, {"id": 147, "dex_number": 147, "name": "dratini", "evolution_chain_id": 76, "base_species": "dratini", "types": ["dragon"], "image_url": "./src/PokemonSprites/147.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": 30, "evolution_item": null, "rarity": "scarce"}, {"id": 148, "dex_number": 148, "name": "dragonair", "evolution_chain_id": 76, "base_species": "dratini", "types": ["dragon"], "image_url": "./src/PokemonSprites/148.png", "de": "Dragonir", "evolution_level": 55, "evolution_item": null, "rarity": "scarce"}, {"id": 149, "dex_number": 149, "name": "dragonite", "evolution_chain_id": 76, "base_species": "dratini", "types": ["dragon", "flying"], "image_url": "./src/PokemonSprites/149.png", "de": "Dragoran", "evolution_level": null, "evolution_item": null, "rarity": "scarce", "flying_offset": -10}, {"id": 150, "dex_number": 150, "name": "mewtwo", "evolution_chain_id": 77, "base_species": "mewtwo", "types": ["psychic"], "image_url": "./src/PokemonSprites/150.png", "de": "<PERSON><PERSON><PERSON>", "evolution_level": null, "evolution_item": null, "rarity": "legendary"}, {"id": 151, "dex_number": 151, "name": "mew", "evolution_chain_id": 78, "base_species": "mew", "types": ["psychic"], "image_url": "./src/PokemonSprites/151.png", "de": "Mew", "evolution_level": null, "evolution_item": null, "rarity": "mythical", "flying_offset": -15}]