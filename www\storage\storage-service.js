// storage/storage-service.js
// Unified storage service that works with both Capacitor Storage and localStorage

import { logger } from '../utils/logger.js';

export class StorageService {
  constructor() {
    this.capacitorAvailable = !!(window.Capacitor?.Plugins?.Storage);
    logger.debug('StorageService initialized', { capacitorAvailable: this.capacitorAvailable });
  }
  
  /**
   * Get a value from storage
   * @param {string} key - The key to get
   * @param {any} defaultValue - The default value if the key doesn't exist
   * @returns {Promise<any>} - The value from storage
   */
  async get(key, defaultValue = null) {
    try {
      if (this.capacitorAvailable) {
        const result = await window.Capacitor.Plugins.Storage.get({ key });
        return result.value ? JSON.parse(result.value) : defaultValue;
      } else {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : defaultValue;
      }
    } catch (e) {
      logger.error(`<PERSON>rror getting ${key} from storage:`, e);
      return defaultValue;
    }
  }
  
  /**
   * Set a value in storage
   * @param {string} key - The key to set
   * @param {any} value - The value to set
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async set(key, value) {
    try {
      const jsonValue = JSON.stringify(value);
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.set({ key, value: jsonValue });
      } else {
        localStorage.setItem(key, jsonValue);
      }
      logger.debug(`Saved to storage: ${key}`);
      return true;
    } catch (e) {
      logger.error(`Error setting ${key} in storage:`, e);
      return false;
    }
  }
  
  /**
   * Remove a value from storage
   * @param {string} key - The key to remove
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async remove(key) {
    try {
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.remove({ key });
      } else {
        localStorage.removeItem(key);
      }
      logger.debug(`Removed from storage: ${key}`);
      return true;
    } catch (e) {
      logger.error(`Error removing ${key} from storage:`, e);
      return false;
    }
  }
  
  /**
   * Clear all values from storage
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async clear() {
    try {
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.clear();
      } else {
        localStorage.clear();
      }
      logger.debug('Storage cleared');
      return true;
    } catch (e) {
      logger.error('Error clearing storage:', e);
      return false;
    }
  }
  
  /**
   * Get all keys from storage
   * @returns {Promise<string[]>} - The keys in storage
   */
  async keys() {
    try {
      if (this.capacitorAvailable) {
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        return keys;
      } else {
        return Object.keys(localStorage);
      }
    } catch (e) {
      logger.error('Error getting keys from storage:', e);
      return [];
    }
  }
}

// Export a singleton instance
export const storageService = new StorageService();
