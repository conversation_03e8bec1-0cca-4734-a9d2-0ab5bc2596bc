# GPS Pokémon App

Eine mobile Anwendung, die Pokémon-Gameplay mit GPS-Funktionalität kombiniert. Die App ermöglicht es Spielern, in der realen Welt zu navigieren, <PERSON> zu fangen, <PERSON> zu bilden und Kämpfe auszutragen.

## Starten im Browser

1. `npm install -g live-server` (falls nicht installiert)
2. `npm start` im Projektordner

## Capacitor & Android

1. `npm install --save @capacitor/core @capacitor/cli`
2. `npx cap init gps-pokemon-app com.example.gpspokemonapp`
3. `npx cap add android`
4. `npx cap copy android`
5. Ö<PERSON><PERSON> das Android Studio Projekt unter `android/`
6. Baue und starte die App auf deinem Smartphone

## Abhängigkeiten
- [Leaflet](https://leafletjs.com/) (über CDN eingebunden)
- [OpenStreetMap](https://www.openstreetmap.org/)
- [Capacitor](https://capacitorjs.com/)
- [Turf.js](https://turfjs.org/) (für geografische Berechnungen)

## Architektur und Aufbau

Die App ist als Web-Anwendung mit Capacitor für native Funktionen auf mobilen Geräten implementiert. Die Hauptkomponenten sind:

### Verzeichnisstruktur

```
gps-pokemon-app/
├── www/                  # Hauptverzeichnis für Web-Inhalte
│   ├── capacitor/        # Capacitor-Plugin-Helfer
│   ├── services/         # Zentrale Dienste (Erfahrungssystem, Kampfberechnung, etc.)
│   ├── state/            # Zustandsverwaltung
│   ├── storage/          # Speicherdienste
│   ├── styles/           # CSS-Dateien
│   ├── ui/               # UI-Komponenten
│   ├── utils/            # Hilfsfunktionen
│   ├── tests/            # Testdateien
│   ├── pokedex-151.json  # Zentrale Pokémon-Datenbank
│   ├── Pokemon.js        # Pokémon-Klasse
│   ├── index.html        # Haupt-HTML-Datei
│   └── app.js            # Haupt-JavaScript-Datei
└── android/              # Android-spezifische Dateien (von Capacitor verwaltet)
```

### Hauptkomponenten

1. **GPS und Karte**: Zeigt die Position des Spielers und Pokémon-Spawns auf einer interaktiven Karte an.
2. **Pokémon-System**: Verwaltet Pokémon-Daten, Fangen, Team-Management und Entwicklung.
3. **Kampfsystem**: Berechnet Kampfergebnisse basierend auf Pokémon-Typen, Levels und Effektivität.
4. **Erfahrungssystem**: Verwaltet Erfahrungspunkte, Level-Aufstiege und Wachstumskurven.
5. **Speichersystem**: Persistiert Spielerdaten, gefangene Pokémon und Teamzusammensetzung.

## Single Source of Truth

Die zentrale Datenquelle (Single Source of Truth) für alle Pokémon-Daten ist der **PokemonManager** (`www/services/pokemon-manager.js`). Diese Klasse verwaltet:

- Alle gefangenen Pokémon
- Team-Zusammensetzung
- Pokémon-Attribute (Level, XP, etc.)

### Schlüsselfunktionen des PokemonManager

- `initialize()`: Lädt Pokémon-Daten aus dem Speicher und initialisiert den Manager
- `getAllPokemon()`: Gibt alle gefangenen Pokémon zurück
- `getTeamPokemon()`: Gibt die Pokémon im Team des Spielers zurück
- `updatePokemon()`: Aktualisiert ein Pokémon und synchronisiert es mit allen Speicherorten
- `saveAllPokemon()`: Speichert alle Pokémon-Daten
- `saveTeam()`: Speichert die aktuelle Team-Zusammensetzung

### Verbindungen zu anderen Komponenten

Der PokemonManager interagiert direkt mit folgenden Komponenten:

1. **Pokemon-Klasse** (`www/Pokemon.js`):
   - Repräsentiert ein einzelnes Pokémon mit Methoden wie `addExperience()`
   - Verwendet den PokemonManager, um Änderungen zu persistieren
   - Stellt sicher, dass Änderungen an Pokémon-Daten im zentralen Speicher aktualisiert werden

2. **StorageService** (`www/storage/storage-service.js`):
   - Bietet eine einheitliche Schnittstelle für Speicheroperationen
   - Wird vom PokemonManager verwendet, um Daten zu laden und zu speichern
   - Unterstützt sowohl lokalen Speicher als auch Capacitor-Speicher für mobile Geräte

3. **BattleScreen** (`www/ui/BattleScreen.js`):
   - Zeigt Kämpfe an und verarbeitet deren Ergebnisse
   - Ruft `addExperience()` auf Pokémon-Objekten auf, um XP nach Kämpfen hinzuzufügen
   - Verwendet den PokemonManager, um aktualisierte Pokémon-Daten zu erhalten

4. **PokemonCaughtScreen** (`www/ui/PokemonCaughtScreen.js`):
   - Zeigt gefangene Pokémon und das Team an
   - Lädt Daten über den PokemonManager
   - Ermöglicht Team-Management (Hinzufügen/Entfernen von Pokémon)

5. **ExperienceSystem** (`www/services/experience-system.js`):
   - Berechnet XP-Anforderungen für Level und Wachstumskurven
   - Wird von der Pokemon-Klasse verwendet, um Level-Aufstiege zu verarbeiten

## Datenfluss

1. **Pokémon fangen**:
   - Ein wildes Pokémon wird generiert
   - Nach einem erfolgreichen Kampf wird es zum PokemonManager hinzugefügt
   - Der PokemonManager speichert es im persistenten Speicher

2. **XP und Level-Aufstiege**:
   - Nach einem Kampf erhält das Pokémon XP über die `addExperience()`-Methode
   - Die Methode aktualisiert die internen XP- und Level-Werte
   - Die Methode ruft `pokemonManager.updatePokemon()` auf, um die Änderungen zu speichern
   - Der PokemonManager aktualisiert sowohl den zentralen Speicher als auch den Team-Speicher

3. **Team-Management**:
   - Änderungen am Team werden über den PokemonManager vorgenommen
   - Der Manager aktualisiert die Team-IDs und speichert die Änderungen
   - Alle UI-Komponenten laden Team-Daten vom PokemonManager

## Wichtige Designprinzipien

1. **Zentrale Datenverwaltung**: Alle Pokémon-Daten werden zentral vom PokemonManager verwaltet, um Inkonsistenzen zu vermeiden.

2. **Konsistente Speicherung**: Änderungen an Pokémon-Daten werden immer in allen relevanten Speicherorten aktualisiert.

3. **Klare Verantwortlichkeiten**: Jede Komponente hat eine klar definierte Rolle im System.

4. **Robuste Fehlerbehandlung**: Alle Operationen enthalten Fehlerbehandlung, um die Datenintegrität zu gewährleisten.

## Wichtige Dateien im Detail

### pokedex-151.json
Diese Datei enthält die zentrale Datenbank aller 151 Pokémon der ersten Generation mit folgenden Informationen:
- Name (englisch und deutsch)
- Pokédex-Nummer
- Typen
- Seltenheit (common, scarce, rare, starter, legendary, mythical)
- Bild-URL
- Entwicklungsstufe und -level
- Entwicklungsketten-ID

Diese Datei dient als Referenz für alle Pokémon-Daten und wird beim Spawnen von wilden Pokémon, beim Erstellen neuer Pokémon-Objekte und bei Entwicklungen verwendet.

### Pokemon.js
Definiert die Pokémon-Klasse, die ein einzelnes Pokémon repräsentiert. Wichtige Funktionen:
- Konstruktor zum Erstellen neuer Pokémon
- Getter und Setter für Eigenschaften wie Erfahrung und Level
- `addExperience()`: Fügt XP hinzu und aktualisiert das Level
- `evolve()`: Führt eine Pokémon-Entwicklung durch
- `toJSON()`: Serialisiert das Pokémon für die Speicherung

### services/pokemon-manager.js
Zentrale Verwaltungsklasse für alle Pokémon-Daten (Single Source of Truth). Verwaltet:
- Laden und Speichern aller Pokémon
- Team-Management
- Pokémon-Aktualisierungen
- Validierung von Pokémon-Daten

### services/pokemon-spawner.js
Verantwortlich für das Spawnen von wilden Pokémon auf der Karte:
- Verwendet Turf.js für geografische Berechnungen
- Berücksichtigt Landnutzungsdaten für typspezifische Spawns
- Implementiert Seltenheitsverteilung und Spawn-Logik

### services/pokemon-grid.js
Implementiert das Pokémon-Gittersystem für die Welt:
- Teilt die Welt in 300m-Zellen auf
- Weist jeder Zelle bestimmte Pokémon-Familien basierend auf Seltenheit zu
- Stellt sicher, dass in bestimmten Gebieten konsistent die gleichen Pokémon-Arten erscheinen
- Berücksichtigt Seltenheitswerte: mythical (0,5%), legendary (1%), starter (4%), rare (6%), scarce (12%), common (Rest)

### services/battle-calc.js
Berechnet Kampfergebnisse zwischen Spieler- und wilden Pokémon:
- Berücksichtigt Pokémon-Typen und Typ-Effektivität
- Berechnet XP-Gewinne basierend auf Level und Seltenheit
- Bestimmt den Gewinner eines Kampfes

### services/experience-system.js
Verwaltet das Erfahrungssystem:
- Definiert Wachstumskurven für verschiedene Seltenheiten
- Berechnet XP-Anforderungen für Level-Aufstiege
- Verarbeitet XP-Gewinne und Level-Aufstiege

### ui/BattleScreen.js
Implementiert die Kampfoberfläche:
- Zeigt Kampfanimationen und -informationen an
- Verarbeitet Kampfergebnisse und XP-Gewinne
- Zeigt Level-Aufstiege und Entwicklungen an

### ui/PokemonCaughtScreen.js
Zeigt gefangene Pokémon und das Team an:
- Ermöglicht das Anzeigen von Pokémon-Details
- Bietet Team-Management-Funktionen
- Zeigt Pokémon-Statistiken und XP-Fortschritt an

### ui/PokedexScreen.js
Implementiert den Pokédex:
- Zeigt alle gesehenen und gefangenen Pokémon an
- Bietet Detailansichten für jedes Pokémon
- Verfolgt den Fortschritt beim Vervollständigen des Pokédex

### ui/MapScreen.js
Hauptbildschirm mit der Karte:
- Zeigt die Spielerposition und Pokémon-Spawns an
- Implementiert die Spielerbewegung und Animation
- Verwaltet die Interaktion mit wilden Pokémon

### utils/logger.js
Zentrales Logging-System:
- Bietet verschiedene Log-Level (debug, info, warn, error)
- Unterstützt sowohl Browser- als auch Capacitor-Umgebungen
- Hilft bei der Fehlersuche und Diagnose

### state/game-state.js
Verwaltet den globalen Spielzustand:
- Lädt und initialisiert wichtige Daten beim App-Start
- Verwaltet Spielereinstellungen und -fortschritt
- Dient als zentraler Zugriffspunkt für spielweite Daten

### capacitor/gps-plugin.js
Wrapper für Capacitor-GPS-Funktionalität:
- Bietet eine einheitliche Schnittstelle für GPS-Zugriff
- Implementiert Fallback-Mechanismen für Browser-Umgebungen
- Verwaltet Berechtigungen und Standortaktualisierungen

### styles/
Enthält alle CSS-Dateien für die App:
- **style.css**: Hauptstilregeln für die gesamte App
- **variables-gui.css**: Zentrale Farbdefinitionen und UI-Variablen
- **pokemon-caught-screen.css**: Stile für den Pokémon-Teambildschirm
- **battle-screen.css**: Stile für den Kampfbildschirm

### src/
Enthält Grafiken und Assets:
- **PokemonSprites/**: Enthält alle Pokémon-Sprites (nach Pokédex-Nummer)
- **battleBackgrounds/**: Hintergrundbilder für Kampfszenen
- **playerSprites/**: Spieler-Sprites (männlich/weiblich) mit Animationsframes für verschiedene Richtungen

## Tests

Die App enthält Tests zur Überprüfung der Datenintegrität, insbesondere:

- **XP-Konsistenztest** (`www/tests/xp-consistency-test.js`): Überprüft, ob XP- und Level-Werte über alle Bildschirme und Operationen hinweg konsistent bleiben.
- **Kampfberechnungstest**: Überprüft die Korrektheit der Kampfberechnungen und Typ-Effektivität.

## Entwicklungshinweise

- Die App benötigt GPS-Berechtigungen auf deinem Smartphone.
- Verwende immer die `addExperience()`-Methode der Pokemon-Klasse, um XP hinzuzufügen, anstatt die Erfahrung direkt zu setzen.
- Verwende immer den PokemonManager, um Pokémon zu aktualisieren, hinzuzufügen oder zu entfernen.
- Stelle sicher, dass UI-Komponenten ihre Daten vom PokemonManager laden, anstatt lokale Kopien zu verwenden.
