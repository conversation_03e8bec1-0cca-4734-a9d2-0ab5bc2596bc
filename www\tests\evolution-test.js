// tests/evolution-test.js
// A simple test script to check Pokemon evolution functionality

import { logger } from '../utils/logger.js';
import Pokemon from '../Pokemon.js';
import fs from 'fs';
import path from 'path';

// Configure logger for testing
logger.level = 'debug';

// Load pokedex data
let pokedexData;
try {
    const pokedexPath = path.join(process.cwd(), 'pokedex-151.json');
    const pokedexContent = fs.readFileSync(pokedexPath, 'utf8');
    pokedexData = JSON.parse(pokedexContent);
    console.log(`Loaded ${pokedexData.length} Pokemon from pokedex`);
} catch (error) {
    console.error('Error loading pokedex data:', error);
    process.exit(1);
}

// Main test function
async function runEvolutionTests() {
    console.log('[INFO] === EVOLUTION TEST ===');

    // Find all Pokemon with evolution data
    const pokemonWithEvolution = pokedexData.filter(p => p.evolution_level !== null);
    console.log(`[INFO] Found ${pokemonWithEvolution.length} Pokemon with evolution data`);

    // Test a few specific evolution cases
    await testSpecificEvolution('bulbasaur', 16, 'ivysaur');
    await testSpecificEvolution('ivysaur', 32, 'venusaur');
    await testSpecificEvolution('charmander', 16, 'charmeleon');
    await testSpecificEvolution('caterpie', 7, 'metapod');
    await testSpecificEvolution('metapod', 10, 'butterfree');

    // Test the Caterpie evolution chain specifically (the one mentioned in the bug report)
    await testEvolutionChain('caterpie');
}

// Test a specific Pokemon evolution
async function testSpecificEvolution(baseName, targetLevel, expectedEvolution) {
    // Find the base Pokemon in the pokedex
    const baseEntry = pokedexData.find(p =>
        p.name.toLowerCase() === baseName.toLowerCase() ||
        (p.de && p.de.toLowerCase() === baseName.toLowerCase())
    );

    if (!baseEntry) {
        console.log(`[ERROR] Could not find ${baseName} in pokedex data`);
        return;
    }

    // Create a Pokemon at the target level
    const pokemon = new Pokemon(baseEntry.name, baseEntry.types[0], targetLevel, {
        types: baseEntry.types,
        evolution_level: baseEntry.evolution_level,
        evolution_item: baseEntry.evolution_item,
        dex: baseEntry.dex_number,
        image_url: baseEntry.image_url,
        rarity: baseEntry.rarity,
        evolution_chain_id: baseEntry.evolution_chain_id
    });

    // Get the display form (which should be the evolved form)
    const displayForm = pokemon.getDisplayForm(pokedexData);

    // Find the expected evolution in the pokedex
    const evolvedEntry = pokedexData.find(p =>
        p.name.toLowerCase() === expectedEvolution.toLowerCase() ||
        (p.de && p.de.toLowerCase() === expectedEvolution.toLowerCase())
    );

    // Check if the evolution is correct
    const evolvedName = evolvedEntry ? (evolvedEntry.de || evolvedEntry.name) : expectedEvolution;
    const actualName = displayForm.name;

    if (actualName.toLowerCase() === evolvedName.toLowerCase()) {
        console.log(`[SUCCESS] ${baseName} (Lvl ${targetLevel}) correctly evolved to ${actualName}`);
    } else {
        console.log(`[ERROR] ${baseName} (Lvl ${targetLevel}) should evolve to ${evolvedName}, but got ${actualName}`);
    }
}

// Test a complete evolution chain
async function testEvolutionChain(baseName) {
    // Find the base Pokemon in the pokedex
    const baseEntry = pokedexData.find(p =>
        p.name.toLowerCase() === baseName.toLowerCase() ||
        (p.de && p.de.toLowerCase() === baseName.toLowerCase())
    );

    if (!baseEntry) {
        console.log(`[ERROR] Could not find ${baseName} in pokedex data`);
        return;
    }

    // Find all Pokemon in the same evolution chain
    const evolutionChain = pokedexData.filter(p => p.evolution_chain_id === baseEntry.evolution_chain_id)
        .sort((a, b) => {
            // Sort by evolution level (null values come last)
            if (a.evolution_level === null && b.evolution_level !== null) return 1;
            if (a.evolution_level !== null && b.evolution_level === null) return -1;
            if (a.evolution_level !== null && b.evolution_level !== null) {
                return a.evolution_level - b.evolution_level;
            }
            return 0;
        });

    console.log(`[INFO] Evolution chain for ${baseName}:`);
    evolutionChain.forEach(p => {
        console.log(`  - ${p.de || p.name} (Lvl ${p.evolution_level || 'MAX'})`);
    });

    // Test each evolution stage
    for (let i = 0; i < evolutionChain.length - 1; i++) {
        const currentStage = evolutionChain[i];
        const nextStage = evolutionChain[i + 1];

        if (currentStage.evolution_level) {
            // Test before evolution level
            await testPokemonAtLevel(currentStage, currentStage.evolution_level - 1, currentStage.de || currentStage.name);

            // Test at evolution level
            await testPokemonAtLevel(currentStage, currentStage.evolution_level, nextStage.de || nextStage.name);

            // Test after evolution level
            await testPokemonAtLevel(currentStage, currentStage.evolution_level + 5, nextStage.de || nextStage.name);
        }
    }
}

// Test a Pokemon at a specific level
async function testPokemonAtLevel(baseEntry, level, expectedName) {
    // Create a Pokemon at the specified level
    const pokemon = new Pokemon(baseEntry.name, baseEntry.types[0], level, {
        types: baseEntry.types,
        evolution_level: baseEntry.evolution_level,
        evolution_item: baseEntry.evolution_item,
        dex: baseEntry.dex_number,
        image_url: baseEntry.image_url,
        rarity: baseEntry.rarity,
        evolution_chain_id: baseEntry.evolution_chain_id
    });

    // Get the display form - make sure to await the async method
    const displayForm = await pokemon.getDisplayForm(pokedexData);

    // Check if the form is correct
    if (displayForm.name.toLowerCase() === expectedName.toLowerCase()) {
        console.log(`[SUCCESS] ${baseEntry.de || baseEntry.name} at level ${level} correctly shows as ${displayForm.name}`);
    } else {
        console.log(`[ERROR] ${baseEntry.de || baseEntry.name} at level ${level} should be ${expectedName}, but got ${displayForm.name}`);
    }
}

// Run the tests
runEvolutionTests().catch(error => {
    console.error('Error running evolution tests:', error);
});
