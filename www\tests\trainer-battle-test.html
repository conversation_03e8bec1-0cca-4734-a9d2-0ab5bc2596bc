<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trainer Battle System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .info {
            color: blue;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .pokemon-team {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .pokemon-card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            background: #f9f9f9;
            min-width: 120px;
        }
        .pokemon-card img {
            width: 50px;
            height: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🥊 Trainer Battle System Test</h1>
        <p>Dieser Test prüft das neue 6-gegen-6 Trainerkampf-System ohne die volle App zu laden.</p>

        <div class="test-section">
            <h3>1. System Initialisierung</h3>
            <button onclick="initializeTest()">System initialisieren</button>
            <div id="init-status"></div>
        </div>

        <div class="test-section">
            <h3>2. Mock-Daten erstellen</h3>
            <button onclick="createMockData()" id="create-mock-btn" disabled>Mock-Daten erstellen</button>
            <div id="mock-status"></div>

            <div id="player-team-display"></div>
            <div id="npc-team-display"></div>
        </div>

        <div class="test-section">
            <h3>3. Battle Session Test</h3>
            <button onclick="testBattleSession()" id="battle-test-btn" disabled>Battle Session testen</button>
            <div id="battle-status"></div>
            <div id="battle-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>4. UI Components Test</h3>
            <button onclick="testTrainerBattleScreen()" id="ui-test-btn" disabled>TrainerBattleScreen testen</button>
            <div id="ui-status"></div>
        </div>

        <div class="test-section">
            <h3>Test Log</h3>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script type="module">
        // Mock implementations for testing
        window.testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            window.testLog.push(logEntry);

            const logDiv = document.getElementById('test-log');
            const logElement = document.createElement('div');
            logElement.className = type;
            logElement.textContent = logEntry;
            logDiv.appendChild(logElement);
            logDiv.scrollTop = logDiv.scrollHeight;

            console.log(logEntry);
        }

        // Mock logger
        window.logger = {
            debug: (msg) => log(`DEBUG: ${msg}`, 'info'),
            info: (msg) => log(`INFO: ${msg}`, 'info'),
            warn: (msg) => log(`WARN: ${msg}`, 'error'),
            error: (msg) => log(`ERROR: ${msg}`, 'error')
        };

        // Mock gameState
        window.gameState = {
            pokedexData: []
        };

        // Mock pokemonManager
        window.pokemonManager = {
            initialize: async () => true,
            getTeamPokemon: () => window.mockPlayerTeam || []
        };

        // Initialize test
        window.initializeTest = async function() {
            try {
                log('Initialisiere Test-System...', 'info');

                // Load pokedex data
                const response = await fetch('./pokedex-151.json');
                const pokedexData = await response.json();
                window.gameState.pokedexData = pokedexData;

                log(`Pokedex geladen: ${pokedexData.length} Pokemon`, 'success');

                document.getElementById('init-status').innerHTML = '<span class="success">✓ System initialisiert</span>';
                document.getElementById('create-mock-btn').disabled = false;

            } catch (error) {
                log(`Fehler bei Initialisierung: ${error.message}`, 'error');
                document.getElementById('init-status').innerHTML = '<span class="error">✗ Initialisierung fehlgeschlagen</span>';
            }
        };

        // Create mock data
        window.createMockData = function() {
            try {
                log('Erstelle Mock-Daten...', 'info');

                // Create player team (6 Pokemon)
                const playerPokemon = [
                    { name: 'pikachu', dex_number: 25, level: 15, types: ['electric'], image: '../src/PokemonSprites/25.png' },
                    { name: 'charizard', dex_number: 6, level: 20, types: ['fire', 'flying'], image: '../src/PokemonSprites/6.png' },
                    { name: 'blastoise', dex_number: 9, level: 18, types: ['water'], image: '../src/PokemonSprites/9.png' },
                    { name: 'venusaur', dex_number: 3, level: 19, types: ['grass', 'poison'], image: '../src/PokemonSprites/3.png' },
                    { name: 'alakazam', dex_number: 65, level: 22, types: ['psychic'], image: '../src/PokemonSprites/65.png' },
                    { name: 'machamp', dex_number: 68, level: 21, types: ['fighting'], image: '../src/PokemonSprites/68.png' }
                ];

                // Create NPC team (6 Pokemon)
                const npcPokemon = [
                    { name: 'raichu', dex_number: 26, level: 16, types: ['electric'], image: '../src/PokemonSprites/26.png' },
                    { name: 'arcanine', dex_number: 59, level: 19, types: ['fire'], image: '../src/PokemonSprites/59.png' },
                    { name: 'gyarados', dex_number: 130, level: 20, types: ['water', 'flying'], image: '../src/PokemonSprites/130.png' },
                    { name: 'vileplume', dex_number: 45, level: 17, types: ['grass', 'poison'], image: '../src/PokemonSprites/45.png' },
                    { name: 'gengar', dex_number: 94, level: 23, types: ['ghost', 'poison'], image: '../src/PokemonSprites/94.png' },
                    { name: 'golem', dex_number: 76, level: 18, types: ['rock', 'ground'], image: '../src/PokemonSprites/76.png' }
                ];

                window.mockPlayerTeam = playerPokemon;
                window.mockNpcTeam = npcPokemon;

                // Display teams
                displayTeam('player-team-display', 'Spieler Team', playerPokemon);
                displayTeam('npc-team-display', 'NPC Team', npcPokemon);

                log('Mock-Daten erstellt: 2 Teams mit je 6 Pokemon', 'success');
                document.getElementById('mock-status').innerHTML = '<span class="success">✓ Mock-Daten erstellt</span>';
                document.getElementById('battle-test-btn').disabled = false;

            } catch (error) {
                log(`Fehler beim Erstellen der Mock-Daten: ${error.message}`, 'error');
                document.getElementById('mock-status').innerHTML = '<span class="error">✗ Mock-Daten Erstellung fehlgeschlagen</span>';
            }
        };

        function displayTeam(containerId, title, team) {
            const container = document.getElementById(containerId);
            let html = `<h4>${title}</h4><div class="pokemon-team">`;

            team.forEach(pokemon => {
                html += `
                    <div class="pokemon-card">
                        <img src="${pokemon.image}" alt="${pokemon.name}" onerror="this.src='../src/PokemonSprites/0.png'">
                        <div><strong>${pokemon.name}</strong></div>
                        <div>Lvl. ${pokemon.level}</div>
                        <div>${pokemon.types.join(', ')}</div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }

        // Test battle session
        window.testBattleSession = async function() {
            try {
                log('Teste Battle Session...', 'info');

                // Import battle session
                const { BattleSession } = await import('../services/battle-session.js');

                const playerTrainer = { name: 'Spieler', team: window.mockPlayerTeam };
                const npcTrainer = { name: 'Test-Trainer', team: window.mockNpcTeam };

                log('Erstelle Battle Session...', 'info');
                const battleSession = new BattleSession(playerTrainer, npcTrainer);

                log('Starte Battle...', 'info');
                const result = await battleSession.start();

                // Display result
                const battleLogDiv = document.getElementById('battle-log');
                battleLogDiv.innerHTML = `
                    <h4>Battle Ergebnis:</h4>
                    <p><strong>Gewinner:</strong> ${result.winner ? result.winner.name : 'Unentschieden'}</p>
                    <p><strong>Runden:</strong> ${result.totalRounds}</p>
                    <h5>Runden-Details:</h5>
                `;

                result.rounds.forEach(round => {
                    const roundDiv = document.createElement('div');
                    roundDiv.innerHTML = `
                        <strong>Runde ${round.round}:</strong>
                        ${round.playerPokemon.name} vs ${round.npcPokemon.name} -
                        ${round.result.playerWins ? 'Spieler gewinnt' : round.result.wasTie ? 'Unentschieden' : 'NPC gewinnt'}
                    `;
                    battleLogDiv.appendChild(roundDiv);
                });

                log(`Battle beendet! Gewinner: ${result.winner ? result.winner.name : 'Unentschieden'}`, 'success');
                document.getElementById('battle-status').innerHTML = '<span class="success">✓ Battle Session Test erfolgreich</span>';
                document.getElementById('ui-test-btn').disabled = false;

            } catch (error) {
                log(`Fehler beim Battle Session Test: ${error.message}`, 'error');
                document.getElementById('battle-status').innerHTML = '<span class="error">✗ Battle Session Test fehlgeschlagen</span>';
            }
        };

        // Test UI components
        window.testTrainerBattleScreen = async function() {
            try {
                log('Teste TrainerBattleScreen...', 'info');

                // Mock back button handler
                window.registerBackButtonHandler = (callback) => {
                    log('Back button handler registriert', 'info');
                    return () => log('Back button handler entfernt', 'info');
                };

                // Import trainer battle screen
                const { TrainerBattleScreen } = await import('../ui/TrainerBattleScreen.js');

                const screen = new TrainerBattleScreen();

                // Test CSS loading
                await screen.loadCSS();
                log('CSS geladen', 'success');

                // Test team requirement check
                const hasTeam = await screen.checkPlayerTeamRequirement();
                log(`Team Requirement Check: ${hasTeam ? 'Erfüllt' : 'Nicht erfüllt'}`, hasTeam ? 'success' : 'error');

                log('TrainerBattleScreen Test erfolgreich', 'success');
                document.getElementById('ui-status').innerHTML = '<span class="success">✓ UI Components Test erfolgreich</span>';

            } catch (error) {
                log(`Fehler beim UI Test: ${error.message}`, 'error');
                document.getElementById('ui-status').innerHTML = '<span class="error">✗ UI Components Test fehlgeschlagen</span>';
            }
        };

        log('Test-System bereit. Klicke auf "System initialisieren" um zu beginnen.', 'info');
    </script>
</body>
</html>
