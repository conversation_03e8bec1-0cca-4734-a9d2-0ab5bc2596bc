// Trainer.js
// Trainer class for GPS Pokemon App

import { logger } from './utils/logger.js';

export default class Trainer {
    /**
     * Create a new Trainer
     * @param {string} trainerType - Type of trainer (e.g., "Youngster")
     * @param {Object} variant - Variant object with sprites and gender
     * @param {string} name - Name of the trainer
     * @param {Array} team - Array of Pokemon
     * @param {Object} levelRange - Level range object with min/max
     */
    constructor(trainerType, variant, name, team, levelRange) {
        this.id = Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
        this.trainerType = trainerType; // z.B. "Youngster"
        this.variant = variant; // Variant-Objekt mit sprites und gender
        this.name = name;
        this.team = team; // Array von Pokemon
        this.levelRange = levelRange;
        
        // Position wird beim Spawnen gesetzt
        this.lat = null;
        this.lng = null;
        this.spawnLat = null;
        this.spawnLng = null;
        
        // Berechne Durchschnitts-Level
        this.averageLevel = this.calculateAverageLevel();
        
        logger.debug(`Created trainer ${this.name} (${this.trainerType}) with ${this.team.length} Pokemon, average level ${this.averageLevel}`);
    }

    /**
     * Calculate the average level of the trainer's team
     * @returns {number} - Average level of the team
     */
    calculateAverageLevel() {
        if (!this.team || this.team.length === 0) {
            return 1;
        }
        
        const totalLevel = this.team.reduce((sum, pokemon) => {
            return sum + (pokemon.level || 1);
        }, 0);
        
        return Math.round(totalLevel / this.team.length);
    }

    /**
     * Set the position of the trainer
     * @param {number} lat - Latitude
     * @param {number} lng - Longitude
     */
    setPosition(lat, lng) {
        this.lat = lat;
        this.lng = lng;
        
        // Set spawn position if not already set
        if (this.spawnLat === null || this.spawnLng === null) {
            this.spawnLat = lat;
            this.spawnLng = lng;
        }
        
        logger.debug(`Set trainer ${this.name} position to ${lat}, ${lng}`);
    }

    /**
     * Create a Trainer from JSON data
     * @param {Object} data - The data to create the Trainer from
     * @returns {Trainer} - A new Trainer instance
     */
    static fromJSON(data) {
        logger.debug(`Deserializing Trainer data for ${data.name || 'unknown'} (ID: ${data.id || 'unknown'})`);

        // Create a new Trainer with basic properties
        const trainer = new Trainer(
            data.trainerType,
            data.variant,
            data.name,
            data.team || [],
            data.levelRange
        );

        // Restore additional properties
        trainer.id = data.id;
        trainer.lat = data.lat;
        trainer.lng = data.lng;
        trainer.spawnLat = data.spawnLat;
        trainer.spawnLng = data.spawnLng;
        trainer.averageLevel = data.averageLevel || trainer.calculateAverageLevel();

        return trainer;
    }

    /**
     * Convert this Trainer to a JSON-serializable object
     * @returns {Object} - A plain object representation of this Trainer
     */
    toJSON() {
        return {
            id: this.id,
            trainerType: this.trainerType,
            variant: this.variant,
            name: this.name,
            team: this.team,
            levelRange: this.levelRange,
            lat: this.lat,
            lng: this.lng,
            spawnLat: this.spawnLat,
            spawnLng: this.spawnLng,
            averageLevel: this.averageLevel
        };
    }

    /**
     * Get the map sprite path for this trainer
     * @returns {string} - Path to the map sprite
     */
    getMapSpritePath() {
        if (!this.variant || !this.variant.mapSprite) {
            return './src/NPCSprites/trainers/default_Map.png'; // Fallback
        }
        return `./src/NPCSprites/trainers/${this.variant.mapSprite}`;
    }

    /**
     * Get the battle sprite path for this trainer
     * @returns {string} - Path to the battle sprite
     */
    getBattleSpritePath() {
        if (!this.variant || !this.variant.battleSprite) {
            return './src/NPCSprites/trainers/default_Battle.png'; // Fallback
        }
        return `./src/NPCSprites/trainers/${this.variant.battleSprite}`;
    }

    /**
     * Get a summary of the trainer's team for display
     * @returns {string} - Team summary string
     */
    getTeamSummary() {
        if (!this.team || this.team.length === 0) {
            return 'Kein Team';
        }

        const teamNames = this.team.map(pokemon => {
            return pokemon.name || 'Unbekannt';
        }).join(', ');

        return `${this.team.length} Pokémon: ${teamNames}`;
    }
}
