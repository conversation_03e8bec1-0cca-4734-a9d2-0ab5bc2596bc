// services/battle-session.js
// Service for managing 6-vs-6 trainer battles

import { logger } from '../utils/logger.js';
import { calculateBattleOutcome } from './battle-calc.js';

export class BattleSession {
  /**
   * Create a new battle session
   * @param {Object} playerTrainer - The player trainer object
   * @param {Object} npcTrainer - The NPC trainer object
   */
  constructor(playerTrainer, npcTrainer) {
    this.playerTrainer = playerTrainer;
    this.npcTrainer = npcTrainer;
    
    // Battle state
    this.currentRound = 0;
    this.playerCurrentPokemonIndex = 0;
    this.npcCurrentPokemonIndex = 0;
    this.battleEnded = false;
    this.winner = null;
    this.rounds = [];
    
    // Pokemon status tracking
    this.playerPokemonStatus = playerTrainer.team.map(() => ({ defeated: false }));
    this.npcPokemonStatus = npcTrainer.team.map(() => ({ defeated: false }));
    
    logger.debug(`Battle session created: ${playerTrainer.name} vs ${npcTrainer.name}`);
  }

  /**
   * Start the battle session
   * @returns {Promise<Object>} - The battle result
   */
  async start() {
    try {
      logger.info(`Starting trainer battle: ${this.playerTrainer.name} vs ${this.npcTrainer.name}`);
      
      // Battle continues until one trainer has no Pokemon left
      while (!this.battleEnded) {
        await this.executeRound();
      }
      
      const result = {
        winner: this.winner,
        playerTrainer: this.playerTrainer,
        npcTrainer: this.npcTrainer,
        rounds: this.rounds,
        totalRounds: this.currentRound
      };
      
      logger.info(`Battle completed. Winner: ${this.winner ? this.winner.name : 'Draw'}`);
      return result;
      
    } catch (error) {
      logger.error('Error during battle session:', error);
      throw error;
    }
  }

  /**
   * Execute a single round of battle
   * @returns {Promise<void>}
   */
  async executeRound() {
    // Get current Pokemon for both trainers
    const playerPokemon = this.getCurrentPlayerPokemon();
    const npcPokemon = this.getCurrentNpcPokemon();
    
    if (!playerPokemon || !npcPokemon) {
      this.endBattle();
      return;
    }
    
    logger.debug(`Round ${this.currentRound + 1}: ${playerPokemon.name} vs ${npcPokemon.name}`);
    
    // Calculate battle outcome using existing battle system
    const battleResult = calculateBattleOutcome(playerPokemon, npcPokemon);
    
    // Record the round
    const roundData = {
      round: this.currentRound + 1,
      playerPokemon: { ...playerPokemon },
      npcPokemon: { ...npcPokemon },
      result: battleResult
    };
    this.rounds.push(roundData);
    
    // Handle round result
    if (battleResult.playerWins) {
      // Player wins - NPC Pokemon is defeated
      this.npcPokemonStatus[this.npcCurrentPokemonIndex].defeated = true;
      logger.debug(`${npcPokemon.name} defeated by ${playerPokemon.name}`);
      
      // Move to next NPC Pokemon
      this.npcCurrentPokemonIndex = this.getNextActivePokemonIndex(this.npcTrainer.team, this.npcPokemonStatus);
      
    } else if (battleResult.wasTie) {
      // Tie - both Pokemon are defeated
      this.playerPokemonStatus[this.playerCurrentPokemonIndex].defeated = true;
      this.npcPokemonStatus[this.npcCurrentPokemonIndex].defeated = true;
      logger.debug(`${playerPokemon.name} and ${npcPokemon.name} both defeated in tie`);
      
      // Move to next Pokemon for both trainers
      this.playerCurrentPokemonIndex = this.getNextActivePokemonIndex(this.playerTrainer.team, this.playerPokemonStatus);
      this.npcCurrentPokemonIndex = this.getNextActivePokemonIndex(this.npcTrainer.team, this.npcPokemonStatus);
      
    } else {
      // NPC wins - Player Pokemon is defeated
      this.playerPokemonStatus[this.playerCurrentPokemonIndex].defeated = true;
      logger.debug(`${playerPokemon.name} defeated by ${npcPokemon.name}`);
      
      // Move to next Player Pokemon
      this.playerCurrentPokemonIndex = this.getNextActivePokemonIndex(this.playerTrainer.team, this.playerPokemonStatus);
    }
    
    this.currentRound++;
    
    // Check if battle should end
    if (this.playerCurrentPokemonIndex === -1 || this.npcCurrentPokemonIndex === -1) {
      this.endBattle();
    }
  }

  /**
   * Get the next active Pokemon index
   * @param {Array} team - The team array
   * @param {Array} statusArray - The status array
   * @returns {number} - The index of the next active Pokemon, or -1 if none
   */
  getNextActivePokemonIndex(team, statusArray) {
    for (let i = 0; i < team.length; i++) {
      if (!statusArray[i].defeated) {
        return i;
      }
    }
    return -1; // No active Pokemon left
  }

  /**
   * End the battle and determine winner
   */
  endBattle() {
    this.battleEnded = true;
    
    const playerHasActivePokemon = this.playerCurrentPokemonIndex !== -1;
    const npcHasActivePokemon = this.npcCurrentPokemonIndex !== -1;
    
    if (playerHasActivePokemon && !npcHasActivePokemon) {
      this.winner = this.playerTrainer;
    } else if (!playerHasActivePokemon && npcHasActivePokemon) {
      this.winner = this.npcTrainer;
    } else {
      // Both have no active Pokemon - draw
      this.winner = null;
    }
    
    logger.debug(`Battle ended. Winner: ${this.winner ? this.winner.name : 'Draw'}`);
  }

  /**
   * Get current player Pokemon
   * @returns {Object|null} - The current player Pokemon or null
   */
  getCurrentPlayerPokemon() {
    if (this.playerCurrentPokemonIndex === -1 || this.playerCurrentPokemonIndex >= this.playerTrainer.team.length) {
      return null;
    }
    return this.playerTrainer.team[this.playerCurrentPokemonIndex];
  }

  /**
   * Get current NPC Pokemon
   * @returns {Object|null} - The current NPC Pokemon or null
   */
  getCurrentNpcPokemon() {
    if (this.npcCurrentPokemonIndex === -1 || this.npcCurrentPokemonIndex >= this.npcTrainer.team.length) {
      return null;
    }
    return this.npcTrainer.team[this.npcCurrentPokemonIndex];
  }

  /**
   * Get battle status for UI updates
   * @returns {Object} - Current battle status
   */
  getBattleStatus() {
    return {
      currentRound: this.currentRound,
      battleEnded: this.battleEnded,
      winner: this.winner,
      playerCurrentPokemon: this.getCurrentPlayerPokemon(),
      npcCurrentPokemon: this.getCurrentNpcPokemon(),
      playerPokemonRemaining: this.playerPokemonStatus.filter(status => !status.defeated).length,
      npcPokemonRemaining: this.npcPokemonStatus.filter(status => !status.defeated).length,
      totalRounds: this.rounds.length
    };
  }

  /**
   * Get detailed battle log
   * @returns {Array} - Array of round data
   */
  getBattleLog() {
    return this.rounds;
  }
}
