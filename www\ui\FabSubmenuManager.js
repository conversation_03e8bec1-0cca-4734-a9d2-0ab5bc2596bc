// ui/FabSubmenuManager.js
// Manager for FAB Submenu

import { logger } from '../utils/logger.js';
import { openEncountersScreen } from './EncountersScreen.js';
import { openPokedexScreen } from './PokedexScreen.js';
import { openPokemonCaughtScreen } from './PokemonCaughtScreen.js';

export class FabSubmenuManager {
  constructor() {
    this.submenuBar = null;
    this.buttons = {};
    this.isActive = false;
  }

  /**
   * Initialize the FAB submenu manager
   */
  initialize() {
    this.ensureSubmenuBar();
    this.setupSubmenuButtons();

    // Make sure submenu is closed initially
    this.isActive = false;
    this.submenuBar.classList.remove('active');

    // Reset any existing state
    const fabBar = document.querySelector('.fab-bar');
    if (fabBar) {
      fabBar.classList.remove('submenu-active');
    }

    logger.debug('FabSubmenuManager initialized');
  }

  /**
   * Ensure the submenu bar exists
   * @returns {HTMLElement} - The submenu bar
   */
  ensureSubmenuBar() {
    let submenuBar = document.querySelector('.fab-submenu');
    if (!submenuBar) {
      submenuBar = document.createElement('div');
      submenuBar.className = 'fab-submenu';
      document.body.appendChild(submenuBar);
    }
    this.submenuBar = submenuBar;
    return submenuBar;
  }

  /**
   * Create a submenu button
   * @param {Object} options - Button options
   * @returns {HTMLElement} - The created button
   */
  createSubmenuButton(options) {
    const { id, title, icon, onClick } = options;

    // Check if button already exists
    if (document.getElementById(id)) return;

    const submenuBar = this.ensureSubmenuBar();
    const btn = document.createElement('button');

    btn.id = id;
    btn.title = title;
    btn.className = 'fab-submenu-btn';
    btn.innerHTML = `<img src="${icon}" alt="${title}" class="icon-svg" />`;
    btn.onclick = onClick;

    submenuBar.appendChild(btn);
    this.buttons[id] = btn;

    return btn;
  }

  /**
   * Setup all submenu buttons
   */
  setupSubmenuButtons() {
    // Ensure submenu bar exists
    this.ensureSubmenuBar();

    // Pokedex button (in pinky-red)
    this.createSubmenuButton({
      id: 'pokedex-submenu-fab',
      title: 'Pokedex öffnen',
      icon: './icons/materialicons/checklist.svg',
      onClick: openPokedexScreen
    });

    // Encounters button (in really-grey)
    this.createSubmenuButton({
      id: 'encounters-submenu-fab',
      title: 'Begegnungen anzeigen',
      icon: './icons/materialicons/eye.svg',
      onClick: openEncountersScreen
    });

    // Pokemon caught button (in bright-yellow)
    this.createSubmenuButton({
      id: 'pokemon-caught-fab',
      title: 'Gefangene Pokemon anzeigen',
      icon: './icons/materialicons/checkball.svg',
      onClick: openPokemonCaughtScreen
    });

    // Close submenu button (in light-grey)
    this.createSubmenuButton({
      id: 'close-submenu-fab',
      title: 'Submenu schließen',
      icon: './icons/materialicons/close.svg',
      onClick: this.closeSubmenu.bind(this)
    });
  }

  /**
   * Toggle the submenu visibility
   */
  toggleSubmenu() {
    // Force a reset of the state first to ensure clean state
    this.resetSubmenuState();

    if (this.isActive) {
      this.closeSubmenu();
    } else {
      this.openSubmenu();
    }

    // Log the current state for debugging
    logger.debug(`Submenu toggled. Active: ${this.isActive}`);
  }

  /**
   * Reset the submenu state to ensure clean state
   */
  resetSubmenuState() {
    // Clear any lingering event listeners or state
    const submenuButtons = document.querySelectorAll('.fab-submenu-btn');
    submenuButtons.forEach(btn => {
      // Ensure buttons are properly initialized
      btn.style.pointerEvents = this.isActive ? 'auto' : 'none';
    });
  }

  /**
   * Open the submenu
   */
  openSubmenu() {
    // Set state first
    this.isActive = true;

    // Update submenu buttons pointer events
    const submenuButtons = document.querySelectorAll('.fab-submenu-btn');
    submenuButtons.forEach(btn => {
      btn.style.pointerEvents = 'auto';
    });

    // Add active class to submenu
    this.submenuBar.classList.add('active');

    // Add submenu-active class to main fab-bar to hide other buttons
    const fabBar = document.querySelector('.fab-bar');
    if (fabBar) {
      fabBar.classList.add('submenu-active');
    }

    logger.debug('Submenu opened');
  }

  /**
   * Close the submenu
   */
  closeSubmenu() {
    // Set state first
    this.isActive = false;

    // Update submenu buttons pointer events
    const submenuButtons = document.querySelectorAll('.fab-submenu-btn');
    submenuButtons.forEach(btn => {
      btn.style.pointerEvents = 'none';
    });

    // Remove active class from submenu
    this.submenuBar.classList.remove('active');

    // Remove submenu-active class from main fab-bar to show other buttons
    const fabBar = document.querySelector('.fab-bar');
    if (fabBar) {
      fabBar.classList.remove('submenu-active');
    }

    logger.debug('Submenu closed');
  }


}

// Export a singleton instance
export const fabSubmenuManager = new FabSubmenuManager();
