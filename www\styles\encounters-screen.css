/* encounters-screen.css */
.encounters-overlay {
    background: var(--standard-background-color, #f4f4f4);
    position: fixed;
    top: 0; left: 0; width: 100vw; height: 100vh;
    z-index: 10020;
    overflow-y: auto;
    padding: 0;
  }
  .encounters-header {
    /* Spezifische Stile für den Encounters-Header, falls nötig */
  }
  .encounters-list {
    list-style: none;
    margin: 0;
    padding: 16px;
  }
  .encounter-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    padding: 12px 16px;
    margin-bottom: 14px;
    font-size: 1.08rem;
  }
  .pokemon-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
  }
  .pokemon-name {
    font-weight: 600;
    color: #40686b;
  }
  .pokemon-level {
    font-size: 0.85rem;
    color: #888;
    margin-left: 8px;
  }
  .encounter-actions {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  .battle-btn {
    background: var(--button-color, #4CAF50);
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  .battle-btn:hover {
    background: var(--button-hover-color, #45a049);
  }
  .battle-btn img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }
  .encounter-date {
    font-size: 0.98rem;
    color: #888;
    margin-left: 14px;
  }
  /* Back-Button-Stile wurden in common-screens.css verschoben */

  .encounters-error,
  .encounters-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--standard-text-color, #333);
  }

  .encounters-error p,
  .encounters-loading p {
    font-size: 1.2rem;
    margin: 10px 0;
  }
