const fs = require('fs');
const path = require('path');

// JSON-Datei mit den Sprite-Daten laden
function loadSpriteData(filename) {
    try {
        const data = fs.readFileSync(filename, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Fehler beim <PERSON> der JSON-Datei:', error);
        return null;
    }
}

// Base64 zu PNG konvertieren
function base64ToPNG(base64String, outputPath) {
    try {
        // Base64-String decodieren
        const imageData = Buffer.from(base64String, 'base64');
        
        // PNG-Datei schreiben
        fs.writeFileSync(outputPath, imageData);
        console.log(`Erstellt: ${outputPath}`);
    } catch (error) {
        console.error(`Fehler bei ${outputPath}:`, error);
    }
}

// Hauptfunktion
function convertAllSprites() {
    console.log('Starte Konvertierung...');
    
    // JSON-Datei laden
    const spriteData = loadSpriteData('sprite_data.json');
    
    if (!spriteData) {
        console.error('Konnte Sprite-Daten nicht laden');
        return;
    }
    
    console.log(`Gefunden: ${Object.keys(spriteData).length} Sprites`);
    
    // Ausgabeordner erstellen (mit recursive Option)
    const outputDir = 'sprites';
    try {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`Ordner "${outputDir}" erstellt/überprüft`);
    } catch (error) {
        console.error('Fehler beim Erstellen des Ordners:', error);
        return;
    }
    
    // Alle Sprites konvertieren
    let convertedCount = 0;
    for (const [filename, base64String] of Object.entries(spriteData)) {
        const outputPath = path.join(outputDir, filename);
        base64ToPNG(base64String, outputPath);
        convertedCount++;
    }
    
    console.log(`Konvertierung abgeschlossen! ${convertedCount} Dateien erstellt.`);
}

// Programm ausführen
convertAllSprites();
