// pokemon-grid.js
// Grid-based Pokemon spawning system

import { config } from './config.js';
import { logger } from './utils/logger.js';
import { gameState } from './state/game-state.js';

// Cell size for the Pokemon grid (meters)
const GRID_CELL_SIZE_METERS = config.grid.cellSizeMeters;

// Rarity priority order (highest to lowest)
const RARITY_PRIORITY = ['mythical', 'legendary', 'starter', 'rare', 'scarce', 'common'];

/**
 * Hash function for grid cells that ensures good distribution
 * @param {number} x - Grid cell X coordinate
 * @param {number} y - Grid cell Y coordinate
 * @returns {number} - The hash value
 */
function hashGridCell(x, y) {
  // Use prime numbers to reduce patterns
  const prime1 = 31;
  const prime2 = 17;

  // Combine x and y in a way that creates more variation
  // This ensures that cells with the same x or y don't have similar hashes
  let hash = ((x * prime1) ^ (y * prime2)) | 0;

  // Add more randomness by using a second mixing step
  hash = ((hash * 747796405) + 2891336453) | 0;
  hash = ((hash >>> 13) ^ hash) * 1597334677 | 0;

  return Math.abs(hash);
}

/**
 * Get the Pokemon index (0-150) for a location
 * @param {number} lat - Latitude in decimal degrees
 * @param {number} lon - Longitude in decimal degrees
 * @returns {number} - Pokemon index (0 to 150)
 */
export function getPokemonIndexForLocation(lat, lon) {
  const metersPerDegLat = 111000;
  const cellDegLat = GRID_CELL_SIZE_METERS / metersPerDegLat;
  const metersPerDegLon = metersPerDegLat * Math.cos(lat * Math.PI / 180);
  const cellDegLon = GRID_CELL_SIZE_METERS / metersPerDegLon;
  const cellY = Math.floor(lat / cellDegLat);
  const cellX = Math.floor(lon / cellDegLon);
  const h = hashGridCell(cellX, cellY);
  const index = (h % 151); // 0…150

  logger.debug(`Grid cell (${cellX},${cellY}) maps to Pokemon index ${index}`);
  return index;
}

/**
 * Get the highest priority rarity in a Pokémon family
 * @param {number} chainId - The evolution chain ID
 * @returns {string} - The highest priority rarity in the family
 */
export function getHighestRarityForChain(chainId) {
  // Get all Pokémon in this family
  const familyPokemon = gameState.pokedexData.filter(p => p.evolution_chain_id === chainId);

  if (!familyPokemon.length) {
    logger.debug(`No Pokémon found for chain ID ${chainId}`);
    return 'common'; // Default to common if no Pokémon found
  }

  // Check for invalid rarity values
  for (const pokemon of familyPokemon) {
    if (!pokemon.rarity) {
      logger.debug(`Pokémon ${pokemon.name} (ID: ${pokemon.id}) has no rarity value`);
      pokemon.rarity = 'common'; // Set default rarity
    } else if (!RARITY_PRIORITY.includes(pokemon.rarity)) {
      logger.debug(`Pokémon ${pokemon.name} (ID: ${pokemon.id}) has invalid rarity value: ${pokemon.rarity}`);
      pokemon.rarity = 'common'; // Set default rarity
    }
  }

  // Find the highest priority rarity in the family
  let highestRarity = 'common';
  let highestPriority = RARITY_PRIORITY.indexOf('common');

  for (const pokemon of familyPokemon) {
    const rarityPriority = RARITY_PRIORITY.indexOf(pokemon.rarity);
    if (rarityPriority < highestPriority) { // Lower index = higher priority
      highestPriority = rarityPriority;
      highestRarity = pokemon.rarity;
    }
  }

  return highestRarity;
}

/**
 * Get the chain index (evolution_chain_id) for a location
 * @param {number} lat - Latitude in decimal degrees
 * @param {number} lon - Longitude in decimal degrees
 * @param {number} numChains - Number of unique chains (e.g., 73 for Gen1)
 * @returns {number} - Chain index (0 to numChains-1)
 */
export function getChainIndexForLocation(lat, lon, numChains) {
  const metersPerDegLat = 111000;
  const cellDegLat = GRID_CELL_SIZE_METERS / metersPerDegLat;
  const metersPerDegLon = metersPerDegLat * Math.cos(lat * Math.PI / 180);
  const cellDegLon = GRID_CELL_SIZE_METERS / metersPerDegLon;
  const cellY = Math.floor(lat / cellDegLat);
  const cellX = Math.floor(lon / cellDegLon);
  const h = hashGridCell(cellX, cellY);

  // Use the hash to determine which rarity category this cell will contain
  const rarityRoll = (h % 1000) / 10; // 0.0-99.9 with one decimal place precision

  // Define probability distribution for different rarities
  // Adjusted based on the number of families per rarity type:
  // mythical: 1 family, legendary: 4 families, starter: 3 families,
  // rare: 8 families, scarce: 7 families, common: 55 families
  //
  // mythical: 0.5%, legendary: 1%, starter: 4%, rare: 6%, scarce: 12%, common: 76.5%
  let rarityFilter;
  if (rarityRoll <= 0.5) {
    rarityFilter = 'mythical';
  } else if (rarityRoll <= 1.5) { // 0.5+1=1.5
    rarityFilter = 'legendary';
  } else if (rarityRoll <= 5.5) { // 1.5+4=5.5
    rarityFilter = 'starter';
  } else if (rarityRoll <= 11.5) { // 5.5+6=11.5
    rarityFilter = 'rare';
  } else if (rarityRoll <= 23.5) { // 11.5+12=23.5
    rarityFilter = 'scarce';
  } else {
    // For the common category (76.5%), we don't need to filter
    rarityFilter = null;
  }

  // Find all chains that match our rarity criteria
  let eligibleChains = [];

  // Get all unique chain IDs from the Pokedex
  const allChainIds = [...new Set(gameState.pokedexData.map(p => p.evolution_chain_id))];

  if (rarityFilter) {
    // Filter chains by specific rarity
    for (const chainId of allChainIds) {
      const highestRarity = getHighestRarityForChain(chainId);
      if (highestRarity === rarityFilter) {
        eligibleChains.push(chainId);
      }
    }
  } else {
    // For null rarityFilter, we want common Pokémon
    for (const chainId of allChainIds) {
      const highestRarity = getHighestRarityForChain(chainId);
      if (highestRarity === 'common') {
        eligibleChains.push(chainId);
      }
    }
  }

  // If no chains match the rarity, fall back to all chains
  if (eligibleChains.length === 0) {
    logger.debug(`No chains found with rarity ${rarityFilter || 'common'}, using all chains`);
    const index = (h % numChains); // 0…numChains-1
    logger.debug(`Grid cell (${cellX},${cellY}) maps to chain index ${index} of ${numChains}`);
    return index;
  }

  // Use the hash to select one of the eligible chains
  const chainIndex = h % eligibleChains.length;
  const selectedChainId = eligibleChains[chainIndex];

  // Find the index of this chain ID in the full chains list
  const fullListIndex = gameState.chainsList.indexOf(selectedChainId);
  if (fullListIndex !== -1) {
    logger.debug(`Grid cell (${cellX},${cellY}) maps to chain index ${fullListIndex} of ${numChains} (rarity: ${rarityFilter || 'common'})`);
    return fullListIndex;
  }

  // Fallback behavior if something went wrong
  const index = (h % numChains); // 0…numChains-1
  logger.debug(`Grid cell (${cellX},${cellY}) maps to chain index ${index} of ${numChains} (fallback)`);
  return index;
}

/**
 * Get the grid cell coordinates for a location
 * @param {number} lat - Latitude in decimal degrees
 * @param {number} lon - Longitude in decimal degrees
 * @returns {Object} - { x, y } grid cell coordinates
 */
export function getGridCellForLocation(lat, lon) {
  const metersPerDegLat = 111000;
  const cellDegLat = GRID_CELL_SIZE_METERS / metersPerDegLat;
  const metersPerDegLon = metersPerDegLat * Math.cos(lat * Math.PI / 180);
  const cellDegLon = GRID_CELL_SIZE_METERS / metersPerDegLon;
  const cellY = Math.floor(lat / cellDegLat);
  const cellX = Math.floor(lon / cellDegLon);

  return { x: cellX, y: cellY };
}

/**
 * Get the center coordinates of a grid cell
 * @param {number} cellX - Grid cell X coordinate
 * @param {number} cellY - Grid cell Y coordinate
 * @param {number} lat - Approximate latitude for calculation
 * @returns {Object} - { lat, lng } center coordinates
 */
export function getGridCellCenter(cellX, cellY, lat) {
  const metersPerDegLat = 111000;
  const cellDegLat = GRID_CELL_SIZE_METERS / metersPerDegLat;
  const metersPerDegLon = metersPerDegLat * Math.cos(lat * Math.PI / 180);
  const cellDegLon = GRID_CELL_SIZE_METERS / metersPerDegLon;

  const centerLat = (cellY + 0.5) * cellDegLat;
  const centerLng = (cellX + 0.5) * cellDegLon;

  return { lat: centerLat, lng: centerLng };
}

