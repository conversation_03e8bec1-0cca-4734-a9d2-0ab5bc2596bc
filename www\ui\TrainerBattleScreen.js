// ui/TrainerBattleScreen.js
// Screen for trainer battles

import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import { pokemonManager } from '../services/pokemon-manager.js';
import { BattleSession } from '../services/battle-session.js';
import { registerBackButtonHandler } from '../capacitor/app.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';

export class TrainerBattleScreen {
    constructor() {
        this.container = null;
        this.currentBattleSession = null;
        this.backButtonHandler = null;
        this.cssLoaded = false;
    }

    /**
     * Load CSS for trainer battle screen
     */
    async loadCSS() {
        if (this.cssLoaded) return;

        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = './styles/trainer-battle.css';
            link.onload = () => {
                this.cssLoaded = true;
                resolve();
            };
            link.onerror = () => reject(new Error('Failed to load trainer battle CSS'));
            document.head.appendChild(link);
        });
    }

    /**
     * Check if player has exactly 6 Pokemon in team
     * @returns {Promise<boolean>} - Whether player has 6 Pokemon
     */
    async checkPlayerTeamRequirement() {
        try {
            await pokemonManager.initialize();
            const playerTeam = pokemonManager.getTeamPokemon();
            return playerTeam && playerTeam.length === 6;
        } catch (e) {
            logger.error('Error checking player team requirement:', e);
            return false;
        }
    }

    /**
     * Start a trainer battle
     * @param {Object} npcTrainer - The NPC trainer to battle
     */
    async startBattle(npcTrainer) {
        try {
            logger.info(`Starting trainer battle against ${npcTrainer.name}`);

            // Load CSS first
            await this.loadCSS();

            // Initialize Pokemon manager
            await pokemonManager.initialize();

            // Check if player has exactly 6 Pokemon
            const hasRequiredTeam = await this.checkPlayerTeamRequirement();
            if (!hasRequiredTeam) {
                alert('Du brauchst genau 6 Pokémon in deinem Team für einen Trainerkampf! Gehe zum Team-Bildschirm und stelle dein Team zusammen.');
                return;
            }

            // Check if NPC trainer has exactly 6 Pokemon
            if (!npcTrainer.team || npcTrainer.team.length !== 6) {
                alert('Der Trainer hat kein vollständiges Team! Kampf kann nicht gestartet werden.');
                return;
            }

            // Get player's team
            const playerTeam = pokemonManager.getTeamPokemon();

            // Create player trainer object
            const playerTrainer = {
                name: 'Spieler',
                team: playerTeam
            };

            // Show battle screen
            this.show();
            // Hide all FAB buttons to prevent errors
            fabManager.hideAllButtons();

            // Show battle intro with trainer sprite
            this.showBattleIntro(npcTrainer, playerTrainer);

        } catch (e) {
            logger.error('Error starting trainer battle:', e);
            alert('Fehler beim Starten des Trainerkampfs: ' + e.message);
            this.hide();
        }
    }

    /**
     * Show the trainer battle screen
     */
    show() {
        // Hide other screens
        const screens = ['map-container', 'pokemon-screen', 'team-screen', 'battle-screen'];
        screens.forEach(screenId => {
            const screen = document.getElementById(screenId);
            if (screen) screen.style.display = 'none';
        });

        // Hide FAB buttons during trainer battle
        const fabContainer = document.getElementById('fab-container');
        if (fabContainer) {
            fabContainer.style.display = 'none';
        }

        // Create battle screen if it doesn't exist
        if (!this.container) {
            this.createBattleScreen();
        }

        this.container.style.display = 'block';

        // Register back button handler
        this.backButtonHandler = registerBackButtonHandler(() => {
            this.hide();
        });
    }

    /**
     * Hide the trainer battle screen
     */
    hide() {
        if (this.container) {
            this.container.style.display = 'none';
        }

        // Show map
        const mapContainer = document.getElementById('map-container');
        if (mapContainer) mapContainer.style.display = 'block';

        // Show FAB buttons again
        const fabContainer = document.getElementById('fab-container');
        if (fabContainer) {
            fabContainer.style.display = 'block';
        }

        // Unregister back button handler
        if (this.backButtonHandler) {
            this.backButtonHandler(); // Call the cleanup function
            this.backButtonHandler = null;
        }

        // Clean up battle session
        this.currentBattleSession = null;
    }

    /**
     * Create the battle screen HTML
     */
    createBattleScreen() {
        this.container = document.createElement('div');
        this.container.id = 'trainer-battle-screen';

        this.container.innerHTML = `
            <div class="trainer-battle-container">
                <div class="trainer-battle-header">
                    <h2 class="trainer-battle-title">Trainerkampf</h2>
                    <button id="trainer-battle-close" class="trainer-battle-close">×</button>
                </div>

                <div id="trainer-battle-content">
                    <!-- Battle Intro Screen -->
                    <div id="battle-intro" class="battle-intro">
                        <div id="trainer-sprite-container" class="trainer-sprite-container">
                            <img id="trainer-battle-sprite" class="trainer-battle-sprite" src="" alt="Trainer">
                        </div>
                        <div id="battle-intro-text" class="battle-intro-text">
                            <h3 id="battle-intro-title">Kampf gegen [Name]</h3>
                            <p>Bereite dich auf den Kampf vor!</p>
                            <button id="start-battle-btn" class="start-battle-btn">Kampf beginnen</button>
                        </div>
                    </div>

                    <!-- Battle Screen -->
                    <div id="battle-main" class="battle-main" style="display: none;">
                        <div id="battle-status" class="battle-status">
                            <p>Bereite dich auf den Kampf vor...</p>
                        </div>

                        <div id="battle-display" class="battle-display">
                            <div id="player-side" class="battle-side">
                                <h3>Du</h3>
                                <div id="player-pokemon-display" class="pokemon-display"></div>
                            </div>

                            <div class="vs-divider">VS</div>

                            <div id="npc-side" class="battle-side">
                                <h3 id="npc-trainer-name">Gegner</h3>
                                <div id="npc-pokemon-display" class="pokemon-display"></div>
                            </div>
                        </div>

                        <div id="battle-log" class="battle-log">
                            <h4>Kampfverlauf:</h4>
                            <div id="battle-log-content"></div>
                        </div>

                        <div id="battle-result" class="battle-result">
                            <h3 id="result-title" class="result-title"></h3>
                            <p id="result-description" class="result-description"></p>
                            <button id="battle-finish-btn" class="battle-finish-btn">Beenden</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(this.container);

        // Add event listeners
        document.getElementById('trainer-battle-close').addEventListener('click', () => this.hide());
        document.getElementById('battle-finish-btn').addEventListener('click', () => this.hide());
        document.getElementById('start-battle-btn').addEventListener('click', () => this.startActualBattle());
    }

    /**
     * Show battle intro with trainer sprite
     * @param {Object} npcTrainer - The NPC trainer
     * @param {Object} playerTrainer - The player trainer
     */
    showBattleIntro(npcTrainer, playerTrainer) {
        // Store trainers for later use
        this.npcTrainer = npcTrainer;
        this.playerTrainer = playerTrainer;

        // Update intro text
        document.getElementById('battle-intro-title').textContent = `Kampf gegen ${npcTrainer.name}`;

        // Set trainer battle sprite
        const trainerSprite = document.getElementById('trainer-battle-sprite');
        if (npcTrainer.getBattleSpritePath) {
            trainerSprite.src = npcTrainer.getBattleSpritePath();
        } else {
            trainerSprite.src = './src/NPCSprites/trainers/default_Battle.png';
        }

        // Show intro, hide main battle
        document.getElementById('battle-intro').style.display = 'block';
        document.getElementById('battle-main').style.display = 'none';
    }

    /**
     * Start the actual battle after intro
     */
    async startActualBattle() {
        try {
            // Hide intro, show main battle
            document.getElementById('battle-intro').style.display = 'none';
            document.getElementById('battle-main').style.display = 'block';

            // Create and start battle session
            this.currentBattleSession = new BattleSession(this.playerTrainer, this.npcTrainer);

            // Show initial battle state
            this.updateBattleDisplay();

            // Execute battle automatically (you can make this step-by-step later)
            const result = await this.currentBattleSession.start();

            // Show battle result
            this.showBattleResult(result);

        } catch (e) {
            logger.error('Error during battle execution:', e);
            alert('Fehler während des Kampfes: ' + e.message);
            this.hide();
        }
    }

    /**
     * Update the battle display
     */
    updateBattleDisplay() {
        if (!this.currentBattleSession) return;

        const status = this.currentBattleSession.getBattleStatus();

        // Update NPC trainer name
        document.getElementById('npc-trainer-name').textContent = this.currentBattleSession.npcTrainer.name;

        // Update battle status
        const statusElement = document.getElementById('battle-status');
        if (status.battleEnded) {
            if (status.winner) {
                statusElement.innerHTML = `<strong>${status.winner.name} gewinnt!</strong>`;
            } else {
                statusElement.innerHTML = '<strong>Unentschieden!</strong>';
            }
        } else {
            statusElement.innerHTML = `Runde ${status.currentRound + 1} - Pokémon bereit!`;
        }

        // Update Pokemon displays
        this.updatePokemonDisplay('player-pokemon-display', status.playerCurrentPokemon, status.playerPokemonRemaining);
        this.updatePokemonDisplay('npc-pokemon-display', status.npcCurrentPokemon, status.npcPokemonRemaining);
    }

    /**
     * Update Pokemon display
     * @param {string} elementId - Element ID to update
     * @param {Object} pokemon - Pokemon to display
     * @param {number} remaining - Number of Pokemon remaining
     */
    updatePokemonDisplay(elementId, pokemon, remaining) {
        const element = document.getElementById(elementId);
        if (!element) return;

        if (pokemon) {
            // Find German name for display
            const displayName = getGermanPokemonName(pokemon) || pokemon.name;

            element.className = 'pokemon-display';
            element.innerHTML = `
                <img src="${pokemon.image || pokemon.image_url || `./src/PokemonSprites/${pokemon.dex_number}.png`}"
                     alt="${displayName}">
                <p class="pokemon-name">${displayName}</p>
                <p class="pokemon-level">Level ${pokemon.level}</p>
                <p class="pokemon-remaining">${remaining} verbleibend</p>
            `;
        } else {
            element.className = 'pokemon-display no-pokemon';
            element.innerHTML = '<p>Keine Pokémon mehr</p>';
        }
    }

    /**
     * Show battle result
     * @param {Object} result - Battle result
     */
    showBattleResult(result) {
        // Update final display
        this.updateBattleDisplay();

        // Add battle log
        this.addBattleLog(result.rounds);

        // Show result section
        const resultSection = document.getElementById('battle-result');
        const resultTitle = document.getElementById('result-title');
        const resultDescription = document.getElementById('result-description');

        if (result.winner) {
            if (result.winner.name === 'Spieler') {
                resultTitle.textContent = 'Sieg!';
                resultTitle.className = 'result-title victory';
                resultDescription.textContent = `Du hast ${result.npcTrainer.name} besiegt!`;
            } else {
                resultTitle.textContent = 'Niederlage!';
                resultTitle.className = 'result-title defeat';
                resultDescription.textContent = `${result.winner.name} hat dich besiegt!`;
            }
        } else {
            resultTitle.textContent = 'Unentschieden!';
            resultTitle.className = 'result-title draw';
            resultDescription.textContent = 'Beide Trainer haben keine Pokémon mehr!';
        }

        resultSection.style.display = 'block';
    }

    /**
     * Add battle log entries
     * @param {Array} rounds - Battle rounds
     */
    addBattleLog(rounds) {
        const logContent = document.getElementById('battle-log-content');
        if (!logContent) return;

        logContent.innerHTML = '';

        rounds.forEach(round => {
            const playerName = getGermanPokemonName(round.playerPokemon) || round.playerPokemon.name;
            const npcName = getGermanPokemonName(round.npcPokemon) || round.npcPokemon.name;

            let resultText = '';
            if (round.result.playerWins) {
                resultText = `${playerName} besiegt ${npcName}!`;
            } else if (round.result.wasTie) {
                resultText = `${playerName} und ${npcName} kämpfen unentschieden!`;
            } else {
                resultText = `${npcName} besiegt ${playerName}!`;
            }

            const logEntry = document.createElement('div');
            logEntry.className = 'battle-log-entry';
            logEntry.innerHTML = `<strong>Runde ${round.round}:</strong> ${resultText}`;
            logContent.appendChild(logEntry);
        });

        // Scroll to bottom
        logContent.scrollTop = logContent.scrollHeight;
    }
}

// Export singleton instance
export const trainerBattleScreen = new TrainerBattleScreen();
