// capacitor/geolocation.js
// Helper module for Capacitor Geolocation with Browser Fallback

import { config } from '../config.js';
import { logger } from '../utils/logger.js';

// Check if we're in test mode (for browser testing without Capacitor)
const isTestMode = window.location.href.includes('test.html');

// Check if Capacitor is available and we're on a native platform
const isCapacitorAvailable = () => {
  return !!(window.Capacitor?.Plugins?.Geolocation && window.Capacitor?.isNativePlatform?.());
};

// Check if browser geolocation is available
const isBrowserGeolocationAvailable = () => {
  return !!(navigator.geolocation);
};

/**
 * Start watching the user's position
 * @param {Function} callback - Callback function(position, error)
 * @returns {Function} - Function to stop watching
 */
export async function startWatchPosition(callback) {
  // In test mode, use mock data
  if (isTestMode) {
    logger.info('Using test mode for geolocation watching');

    // For testing, immediately return a fixed position
    const mockPosition = {
      coords: {
        latitude: 52.520008,
        longitude: 13.404954,
        accuracy: 10,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    };

    // Call the callback with the mock position
    setTimeout(() => {
      logger.debug('Test mode: sending mock position');
      callback(mockPosition);
    }, 100);

    // Return a no-op stop function
    return () => {
      logger.info('Test mode: stopping position watch (no-op)');
    };
  }

  // Check if Capacitor is available
  if (isCapacitorAvailable()) {
    logger.info('Using Capacitor Geolocation for position watching');
    return startCapacitorWatchPosition(callback);
  }

  // Fallback to browser geolocation
  if (isBrowserGeolocationAvailable()) {
    logger.info('Using Browser Geolocation API for position watching');
    return startBrowserWatchPosition(callback);
  }

  // No geolocation available
  logger.error('No geolocation method available');
  callback(null, { code: 0, message: 'No geolocation method available.' });
  return () => {};
}

/**
 * Start watching position using Capacitor
 * @param {Function} callback - Callback function(position, error)
 * @returns {Function} - Function to stop watching
 */
async function startCapacitorWatchPosition(callback) {
  const Geolocation = window.Capacitor.Plugins.Geolocation;
  let watchId = null;

  try {
    // Get options from config
    const { enableHighAccuracy, timeout, maximumAge } = config.geolocation;

    watchId = await Geolocation.watchPosition({
      enableHighAccuracy,
      timeout,
      maximumAge
    }, (pos, err) => {
      if (err) {
        logger.error('Capacitor Geolocation error:', err);
        callback(null, err);
      } else {
        logger.debug(`Capacitor Geolocation update: lat=${pos.coords.latitude}, lng=${pos.coords.longitude}, accuracy=${pos.coords.accuracy}`);
        callback(pos);
      }
    });

    logger.info('Started Capacitor position watch with ID:', watchId);
  } catch (e) {
    logger.error('Error starting Capacitor position watch:', e);
    callback(null, e);
  }

  // Return stop function
  return () => {
    if (Geolocation && watchId != null) {
      logger.info('Stopping Capacitor position watch with ID:', watchId);
      Geolocation.clearWatch({ id: watchId });
    }
  };
}

/**
 * Start watching position using Browser Geolocation API
 * @param {Function} callback - Callback function(position, error)
 * @returns {Function} - Function to stop watching
 */
function startBrowserWatchPosition(callback) {
  // Get options from config
  const { enableHighAccuracy, timeout, maximumAge } = config.geolocation;

  const options = {
    enableHighAccuracy,
    timeout,
    maximumAge
  };

  const watchId = navigator.geolocation.watchPosition(
    (position) => {
      logger.debug(`Browser Geolocation update: lat=${position.coords.latitude}, lng=${position.coords.longitude}, accuracy=${position.coords.accuracy}`);
      callback(position);
    },
    (error) => {
      logger.error('Browser Geolocation error:', error);
      callback(null, error);
    },
    options
  );

  logger.info('Started Browser position watch with ID:', watchId);

  // Return stop function
  return () => {
    logger.info('Stopping Browser position watch with ID:', watchId);
    navigator.geolocation.clearWatch(watchId);
  };
}

/**
 * Get the user's current position once
 * @returns {Promise<Object>} - The position object
 */
export async function getCurrentPosition() {
  // In test mode, use mock data
  if (isTestMode) {
    return new Promise((resolve) => {
      // For testing, return a fixed position in Berlin
      const mockPosition = {
        coords: {
          latitude: 52.520008,
          longitude: 13.404954,
          accuracy: 10,
          altitude: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null
        },
        timestamp: Date.now()
      };

      logger.debug(`Using mock position for testing: lat=${mockPosition.coords.latitude}, lng=${mockPosition.coords.longitude}`);

      resolve(mockPosition);
    });
  }

  // Check if Capacitor is available
  if (isCapacitorAvailable()) {
    logger.info('Using Capacitor Geolocation for current position');
    return await getCapacitorCurrentPosition();
  }

  // Fallback to browser geolocation
  if (isBrowserGeolocationAvailable()) {
    logger.info('Using Browser Geolocation API for current position');
    return await getBrowserCurrentPosition();
  }

  // No geolocation available
  logger.error('No geolocation method available');
  throw new Error('No geolocation method available.');
}

/**
 * Get current position using Capacitor
 * @returns {Promise<Object>} - The position object
 */
async function getCapacitorCurrentPosition() {
  const Geolocation = window.Capacitor.Plugins.Geolocation;

  try {
    // Get options from config
    const { enableHighAccuracy, timeout, maximumAge } = config.geolocation;

    const position = await Geolocation.getCurrentPosition({
      enableHighAccuracy,
      timeout,
      maximumAge
    });

    logger.debug(`Got Capacitor current position: lat=${position.coords.latitude}, lng=${position.coords.longitude}, accuracy=${position.coords.accuracy}`);

    return position;
  } catch (e) {
    logger.error('Error getting Capacitor current position:', e);
    throw e;
  }
}

/**
 * Get current position using Browser Geolocation API
 * @returns {Promise<Object>} - The position object
 */
async function getBrowserCurrentPosition() {
  return new Promise((resolve, reject) => {
    // Get options from config
    const { enableHighAccuracy, timeout, maximumAge } = config.geolocation;

    const options = {
      enableHighAccuracy,
      timeout,
      maximumAge
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        logger.debug(`Got Browser current position: lat=${position.coords.latitude}, lng=${position.coords.longitude}, accuracy=${position.coords.accuracy}`);
        resolve(position);
      },
      (error) => {
        logger.error('Error getting Browser current position:', error);
        reject(error);
      },
      options
    );
  });
}

/**
 * Check if location permissions are granted
 * @returns {Promise<boolean>} - Whether permissions are granted
 */
export async function checkLocationPermissions() {
  // In test mode, always return true
  if (isTestMode) {
    logger.info('Test mode: location permissions check (always granted)');
    return true;
  }

  // Check if Capacitor is available
  if (isCapacitorAvailable()) {
    logger.info('Checking Capacitor location permissions');
    return await checkCapacitorLocationPermissions();
  }

  // For browser, we can't really check permissions beforehand
  // The browser will prompt when we try to access geolocation
  if (isBrowserGeolocationAvailable()) {
    logger.info('Browser geolocation available - permissions will be requested when needed');
    return true; // Assume available, browser will prompt if needed
  }

  logger.error('No geolocation method available for permission check');
  return false;
}

/**
 * Request location permissions
 * @returns {Promise<boolean>} - Whether permissions are granted
 */
export async function requestLocationPermissions() {
  // In test mode, always return true
  if (isTestMode) {
    logger.info('Test mode: location permissions request (always granted)');
    return true;
  }

  // Check if Capacitor is available
  if (isCapacitorAvailable()) {
    logger.info('Requesting Capacitor location permissions');
    return await requestCapacitorLocationPermissions();
  }

  // For browser, we can't explicitly request permissions
  // The browser will prompt when we try to access geolocation
  if (isBrowserGeolocationAvailable()) {
    logger.info('Browser geolocation available - permissions will be requested when accessing location');
    return true; // Browser will handle permission request automatically
  }

  logger.error('No geolocation method available for permission request');
  return false;
}

/**
 * Check Capacitor location permissions
 * @returns {Promise<boolean>} - Whether permissions are granted
 */
async function checkCapacitorLocationPermissions() {
  const Geolocation = window.Capacitor.Plugins.Geolocation;

  try {
    const permissions = await Geolocation.checkPermissions();
    const granted = permissions.location === 'granted';

    logger.info('Capacitor location permissions:', permissions.location);
    return granted;
  } catch (e) {
    logger.error('Error checking Capacitor location permissions:', e);
    return false;
  }
}

/**
 * Request Capacitor location permissions
 * @returns {Promise<boolean>} - Whether permissions are granted
 */
async function requestCapacitorLocationPermissions() {
  const Geolocation = window.Capacitor.Plugins.Geolocation;

  try {
    const permissions = await Geolocation.requestPermissions();
    const granted = permissions.location === 'granted';

    logger.info('Capacitor location permissions after request:', permissions.location);
    return granted;
  } catch (e) {
    logger.error('Error requesting Capacitor location permissions:', e);
    return false;
  }
}
