// Mapping von OSM Landnutzung / Natur / Freizeit zu Pokémon-Typen
// plus Farbcode für Marker-Visualisierung

export const LANDUSE_TYPE_MAPPING = {
  // landuse-Tags
  flowerbed:        { type: 'grass',      color: 'var(--type-grass)' }, // Blumenbeet
  greenhouse_horticulture: { type: 'grass', color: 'var(--type-grass)' }, // Gewächshaus
  allotments:       { type: 'grass',      color: 'var(--type-grass)' }, // Kleingarten
  farmyard:         { type: 'normal',       color: 'var(--type-normal)' }, // Hofbereich
  paddy:            { type: 'water',       color: 'var(--type-water)' }, // Reisfeld
  aquaculture:      { type: 'water',       color: 'var(--type-water)' }, // Aquakultur
  animal_keeping:   { type: 'normal',       color: 'var(--type-normal)' }, // Tierhaltung
  logging:          { type: 'rock',      color: 'var(--type-rock)' }, // Holzeinschlag
  education:        { type: 'psychic',       color: 'var(--type-psychic)' }, // Schule/Uni
  institutional:    { type: 'psychic',       color: 'var(--type-psychic)' }, // Institutionen
  civic_admin:      { type: 'psychic',       color: 'var(--type-psychic)' }, // Verwaltung
  fairground:       { type: 'fairy',          color: 'var(--type-fairy)' }, // Jahrmarkt
  fair:             { type: 'fairy',          color: 'var(--type-fairy)' }, // Jahrmarkt (selten)
  plant_nursery:    { type: 'grass',      color: 'var(--type-grass)' }, // Baumschule
  salt_pond:        { type: 'water',       color: 'var(--type-water)' }, // Salzbecken
  depot:            { type: 'steel',        color: 'var(--type-steel)' }, // Depot
  garages:          { type: 'steel',        color: 'var(--type-steel)' }, // Garagen
  forest:         { type: 'bug',         color: 'var(--type-bug)' },
  farmland:       { type: 'ground',         color: 'var(--type-ground)' },
  arable:         { type: 'ground',         color: 'var(--type-ground)' },
  pasture:       { type: 'ground',         color: 'var(--type-ground)' },
  grass:          { type: 'grass',           color: 'var(--type-grass)' },
  meadow:         { type: 'normal',        color: 'var(--type-normal)' },
  residential:    { type: 'normal',        color: 'var(--type-normal)' },
  industrial:     { type: 'steel',         color: 'var(--type-steel)' },
  commercial:     { type: 'psychic',        color: 'var(--type-psychic)' },
  railway:        { type: 'electric',       color: 'var(--type-electric)' },
  retail:         { type: 'normal',        color: 'var(--type-normal)' },
  cemetery:       { type: 'ghost',         color: 'var(--type-ghost)' },
  military:       { type: 'fighting',         color: 'var(--type-fighting)' },
  quarry:         { type: 'rock',       color: 'var(--type-rock)' },
  construction:   { type: 'steel',         color: 'var(--type-steel)' },
  brownfield:     { type: 'poison',          color: 'var(--type-poison)' },
  greenfield:     { type: 'grass',       color: 'var(--type-grass)' },
  recreation_ground: { type: 'fighting',      color: 'var(--type-fighting)' },
  orchard:        { type: 'grass',       color: '#c0ca33' },
  vineyard:       { type: 'grass',       color: '#7cb342' },
  farmland_orchard: { type: 'grass',     color: '#c0ca33' },

  // natural-Tags
  water:          { type: 'water',        color: 'var(--type-water)' },
  wood:           { type: 'grass',       color: '#2e7d32' },
  beach:          { type: 'ground',         color: '#fbc02d' },
  bare_rock:      { type: 'rock',       color: 'var(--type-ground)' },
  scrub:          { type: 'bug',         color: '#7cb342' },
  sand:           { type: 'ground',         color: '#fdd835' },
  glacier:        { type: 'ice',           color: '#b3e5fc' },
  cliff:          { type: 'rock',       color: '#616161' },
  fell:           { type: 'ground',       color: 'var(--type-ground)' },    // baumlose Hochfläche
  grassland:      { type: 'grass',        color: 'var(--type-grass)' },    // natürliche Grasfläche
  heath:          { type: 'grass',        color: 'var(--type-grass)' },    // Heide
  moor:           { type: 'ghost',        color: '#8e24aa' },    // Moor
  shrubbery:      { type: 'bug',          color: '#7cb342' },    // Gebüsch
  tree:           { type: 'grass',        color: 'var(--type-grass)' },    // einzelner Baum
  tree_row:       { type: 'grass',        color: 'var(--type-grass)' },    // Baumreihe
  tree_stump:     { type: 'rock',         color: 'var(--type-rock)' },    // Baumstumpf
  tundra:         { type: 'ice',          color: '#b3e5fc' },    // Tundra
  bay:            { type: 'water',        color: 'var(--type-water)' },    // Bucht
  blowhole:       { type: 'water',        color: 'var(--type-water)' },    // Geysir/Blowhole
  cape:           { type: 'rock',         color: 'var(--type-rock)' },    // Landzunge/Kap
  coastline:      { type: 'water',        color: 'var(--type-water)' },    // Küstenlinie
  crevasse:       { type: 'ice',          color: '#b3e5fc' },    // Gletscherspalte
  geyser:         { type: 'water',        color: 'var(--type-water)' },    // Geysir
  hot_spring:     { type: 'water',        color: 'var(--type-water)' },    // heiße Quelle
  isthmus:        { type: 'ground',       color: 'var(--type-ground)' },    // Landenge
  mud:            { type: 'ground',       color: 'var(--type-ground)' },    // Schlamm
  wetland:        { type: 'water',        color: 'var(--type-water)' },    // Feuchtgebiet/Sumpf

  // leisure-Tags
  park:           { type: 'grass',       color: '#66bb6a' },
  garden:         { type: 'grass',       color: '#9ccc65' },
  pitch:          { type: 'fighting',         color: '#fbc02d' },
  playground:     { type: 'fairy',           color: '#ffccbc' },
  stadium:        { type: 'fighting',         color: '#ff7043' },
  golf_course:    { type: 'flying',          color: 'var(--type-grass)' },
  sports_centre:  { type: 'fighting',         color: '#ef5350' },
  amusement_arcade:   { type: 'electric', color: 'var(--type-electric)' },   // Spielhalle
  adult_gaming_centre:{ type: 'psychic',  color: 'var(--type-psychic)' },   // Spielcasino
  bandstand:          { type: 'fairy',    color: '#ffccbc' },   // Musikpavillon
  bathing_place:      { type: 'water',    color: 'var(--type-water)' },   // Badestelle
  beach_resort:       { type: 'water',    color: 'var(--type-water)' },   // Strandbad
  bird_hide:          { type: 'flying',   color: 'var(--type-grass)' },   // Vogelbeobachtung
  bleachers:          { type: 'fighting', color: '#ef5350' },   // Tribüne
  bowling_alley:      { type: 'rock',     color: 'var(--type-rock)' },   // Bowlingbahn
  common:             { type: 'normal',   color: 'var(--type-normal)' },   // Allmende/Wiese
  dance:              { type: 'fairy',    color: '#ffccbc' },   // Tanzfläche
  disc_golf_course:   { type: 'flying',   color: 'var(--type-grass)' },   // Discgolf
  dog_park:           { type: 'normal',   color: 'var(--type-normal)' },   // Hundewiese
  escape_game:        { type: 'psychic',  color: 'var(--type-psychic)' },   // Escape Room
  firepit:            { type: 'fire',     color: '#ef5350' },   // Feuerstelle
  fishing:            { type: 'water',    color: 'var(--type-water)' },   // Angeln
  fitness_centre:     { type: 'fighting', color: 'var(--type-fighting)' },   // Fitnessstudio
  fitness_station:    { type: 'fighting', color: 'var(--type-fighting)' },   // Trimm-Dich-Pfad
  hackerspace:        { type: 'electric', color: 'var(--type-electric)' },   // Hackerspace
  horse_riding:       { type: 'ground',   color: 'var(--type-ground)' },   // Reitplatz
  ice_rink:           { type: 'ice',      color: '#b3e5fc' },   // Eislaufbahn
  marina:             { type: 'water',    color: 'var(--type-water)' },   // Yachthafen
  miniature_golf:     { type: 'grass',    color: 'var(--type-grass)' },   // Minigolf
  nature_reserve:     { type: 'grass',    color: 'var(--type-grass)' },   // Naturschutzgebiet

  // Wasserflächen
  basin:          { type: 'water',        color: '#03a9f4' },
  reservoir:      { type: 'water',        color: '#0277bd' },
  canal:          { type: 'water',        color: '#00bcd4' },
  riverbank:      { type: 'water',        color: '#4fc3f7' }
};
