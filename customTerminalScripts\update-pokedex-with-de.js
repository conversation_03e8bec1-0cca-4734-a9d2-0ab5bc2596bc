// update-pokedex-with-de.js
// <PERSON><PERSON><PERSON> to add the German name ("de") to each Pokémon in pokedex-1-151-full.json using SimplifiedPokemonData.json

import fs from "fs/promises";

async function main() {
  // Read both files
  const pokedexRaw = await fs.readFile("pokedex-1-151-full.json", "utf-8");
  const simplifiedRaw = await fs.readFile("./www/src/PokemonDataSet/SimplifiedPokemonData.json", "utf-8");
  const pokedex = JSON.parse(pokedexRaw);
  const simplified = JSON.parse(simplifiedRaw);

  // Build a lookup map from id/number to German name
  const deMap = {};
  for (const entry of simplified) {
    // Some numbers may have leading zeros, so match by id/number as integer
    deMap[parseInt(entry.number, 10)] = entry.de;
  }

  // Add the German name to each entry
  for (const mon of pokedex) {
    mon.de = deMap[mon.id] || null;
  }

  // Write the updated file
  await fs.writeFile("pokedex-1-151-full.json", JSON.stringify(pokedex, null, 2), "utf-8");
  console.log("Updated pokedex-1-151-full.json with German names.");
}

main().catch(console.error);
