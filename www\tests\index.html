<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spawn Level & Evolution Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin-bottom: 20px;
        }
        .test-button {
            display: block;
            width: 100%;
            padding: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        #log-container {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .log-info {
            color: #333;
        }
        .log-debug {
            color: #666;
        }
        .log-warn {
            color: #ff9800;
        }
        .log-error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Spawn Level & Evolution Test</h1>
        
        <div class="test-section">
            <button id="run-tests" class="test-button">Run All Tests</button>
            <button id="clear-log" class="test-button">Clear Log</button>
        </div>
        
        <div id="log-container"></div>
    </div>

    <script type="module">
        import { runTests } from './spawn-levels-test.js';
        import { logger } from '../utils/logger.js';
        
        // Override console methods to capture logs
        const logContainer = document.getElementById('log-container');
        
        // Custom logger implementation for the test page
        const originalConsole = {
            log: console.log,
            debug: console.debug,
            info: console.info,
            warn: console.warn,
            error: console.error
        };
        
        // Override console methods
        console.log = function(...args) {
            addLogEntry('log', args);
            originalConsole.log.apply(console, args);
        };
        
        console.debug = function(...args) {
            addLogEntry('debug', args);
            originalConsole.debug.apply(console, args);
        };
        
        console.info = function(...args) {
            addLogEntry('info', args);
            originalConsole.info.apply(console, args);
        };
        
        console.warn = function(...args) {
            addLogEntry('warn', args);
            originalConsole.warn.apply(console, args);
        };
        
        console.error = function(...args) {
            addLogEntry('error', args);
            originalConsole.error.apply(console, args);
        };
        
        // Override logger methods
        logger.debug = function(message, ...args) {
            const fullMessage = args.length > 0 ? `${message} ${args.map(a => JSON.stringify(a)).join(' ')}` : message;
            addLogEntry('debug', [`[DEBUG] ${fullMessage}`]);
            originalConsole.debug(`[DEBUG] ${fullMessage}`);
        };
        
        logger.info = function(message, ...args) {
            const fullMessage = args.length > 0 ? `${message} ${args.map(a => JSON.stringify(a)).join(' ')}` : message;
            addLogEntry('info', [`[INFO] ${fullMessage}`]);
            originalConsole.info(`[INFO] ${fullMessage}`);
        };
        
        logger.warn = function(message, ...args) {
            const fullMessage = args.length > 0 ? `${message} ${args.map(a => JSON.stringify(a)).join(' ')}` : message;
            addLogEntry('warn', [`[WARN] ${fullMessage}`]);
            originalConsole.warn(`[WARN] ${fullMessage}`);
        };
        
        logger.error = function(message, ...args) {
            const fullMessage = args.length > 0 ? `${message} ${args.map(a => JSON.stringify(a)).join(' ')}` : message;
            addLogEntry('error', [`[ERROR] ${fullMessage}`]);
            originalConsole.error(`[ERROR] ${fullMessage}`);
        };
        
        // Add log entry to the log container
        function addLogEntry(level, args) {
            const entry = document.createElement('div');
            entry.className = `log-${level}`;
            entry.textContent = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Event listeners
        document.getElementById('run-tests').addEventListener('click', () => {
            addLogEntry('info', ['Starting tests...']);
            runTests();
        });
        
        document.getElementById('clear-log').addEventListener('click', () => {
            logContainer.innerHTML = '';
        });
    </script>
</body>
</html>
