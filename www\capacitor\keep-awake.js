// capacitor/keep-awake.js
// Helper module for Capacitor Keep Awake plugin

import { logger } from '../utils/logger.js';

/**
 * Keep the screen awake
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function keepScreenAwake() {
  const KeepAwake = window.Capacitor?.Plugins?.KeepAwake;

  if (!KeepAwake) {
    logger.error('Capacitor KeepAwake Plugin not available');
    return false;
  }

  try {
    await KeepAwake.keepAwake();
    logger.info('Screen will stay awake');
    return true;
  } catch (e) {
    logger.error('Error keeping screen awake:', e);
    return false;
  }
}

/**
 * Allow the screen to sleep
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function allowScreenSleep() {
  const KeepAwake = window.Capacitor?.Plugins?.KeepAwake;

  if (!KeepAwake) {
    logger.error('Capacitor KeepAwake Plugin not available');
    return false;
  }

  try {
    await KeepAwake.allowSleep();
    logger.info('Screen can sleep now');
    return true;
  } catch (e) {
    logger.error('Error allowing screen to sleep:', e);
    return false;
  }
}

/**
 * Check if the screen is kept awake
 * @returns {Promise<boolean>} - Whether the screen is kept awake
 */
export async function isScreenKeptAwake() {
  const KeepAwake = window.Capacitor?.Plugins?.KeepAwake;

  if (!KeepAwake) {
    logger.error('Capacitor KeepAwake Plugin not available');
    return false;
  }

  try {
    const { isKeptAwake } = await KeepAwake.isKeptAwake();
    logger.info('Screen kept awake status:', isKeptAwake);
    return isKeptAwake;
  } catch (e) {
    logger.error('Error checking if screen is kept awake:', e);
    return false;
  }
}
