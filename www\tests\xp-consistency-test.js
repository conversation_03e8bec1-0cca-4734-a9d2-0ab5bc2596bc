// tests/xp-consistency-test.js
// Test for Pokemon XP consistency across different screens and battles

import { logger } from '../utils/logger.js';
import { pokemonManager } from '../services/pokemon-manager.js';
import { calculateBattleOutcome } from '../services/battle-calc.js';
import { experienceSystem } from '../services/experience-system.js';
import { storageService } from '../storage/storage-service.js';
import { config } from '../config.js';
import { gameState } from '../state/game-state.js';

// Load pokedex data directly
let pokedexData = null;

// Create a test Pokemon if none exists in the team
async function createTestPokemon() {
  try {
    // Make sure pokedex data is loaded
    if (!pokedexData) {
      pokedexData = await loadPokedexData();
    }

    // Create a test Pikachu
    const Pokemon = (await import('../Pokemon.js')).default;

    // Find Pikachu in pokedex data
    const pikachuData = pokedexData.find(p => p.name === 'Pikachu' || p.dex_number === 25);

    // Create Pokemon with data from pokedex
    const testPokemon = new Pokemon(
      pikachuData?.name || 'Pikachu',
      pikachuData?.types?.[0] || 'electric',
      5,
      {
        types: pikachuData?.types || ['electric'],
        dex: pikachuData?.dex_number || 25,
        image_url: pikachuData?.image_url || './src/PokemonSprites/25.png',
        rarity: pikachuData?.rarity || 'starter',
        evolution_chain_id: pikachuData?.evolution_chain_id || 10
      }
    );

    // Set unique ID for test
    testPokemon.id = 'test-pikachu-xp-test';

    // Set initial experience
    testPokemon.experience = 125; // Level 5 starter Pokemon

    logger.debug(`Created test Pokemon: ${testPokemon.name} (ID: ${testPokemon.id}, Level: ${testPokemon.level}, XP: ${testPokemon.experience})`);

    // Add to storage
    await pokemonManager.addPokemon(testPokemon);
    logger.debug(`Added test Pokemon to storage`);

    // Add to team if team is empty
    const teamPokemon = pokemonManager.getTeamPokemon();
    if (!teamPokemon || teamPokemon.length === 0) {
      const result = await pokemonManager.addToTeam(testPokemon.id);
      logger.debug(`Added test Pokemon to team: ${result.success ? 'Success' : 'Failed'}`);
    }

    return testPokemon;
  } catch (error) {
    logger.error('Error creating test Pokemon:', error);
    return null;
  }
}

/**
 * Load pokedex data from the correct location
 */
async function loadPokedexData() {
  try {
    if (pokedexData) return pokedexData;

    // Try to load from the correct path
    const response = await fetch('../pokedex-151.json');
    if (!response.ok) {
      throw new Error(`Failed to load pokedex data: ${response.status} ${response.statusText}`);
    }

    pokedexData = await response.json();
    logger.debug(`Loaded ${pokedexData.length} Pokemon from pokedex data`);
    return pokedexData;
  } catch (error) {
    logger.error('Error loading pokedex data:', error);
    // Create minimal pokedex data for testing
    return [
      {
        "name": "Pikachu",
        "de": "Pikachu",
        "dex_number": 25,
        "types": ["electric"],
        "rarity": "starter",
        "image_url": "./src/PokemonSprites/25.png",
        "evolution_level": 30,
        "evolution_chain_id": 10
      },
      {
        "name": "Raichu",
        "de": "Raichu",
        "dex_number": 26,
        "types": ["electric"],
        "rarity": "starter",
        "image_url": "./src/PokemonSprites/26.png",
        "evolution_chain_id": 10
      },
      {
        "name": "Rattata",
        "de": "Rattfratz",
        "dex_number": 19,
        "types": ["normal"],
        "rarity": "common",
        "image_url": "./src/PokemonSprites/19.png",
        "evolution_level": 20,
        "evolution_chain_id": 7
      }
    ];
  }
}

/**
 * Run a test to check XP consistency across different operations
 */
export async function testXpConsistency() {
  try {
    logger.debug('=== STARTING XP CONSISTENCY TEST ===');

    // Step 1: Load pokedex data and initialize the Pokemon manager
    pokedexData = await loadPokedexData();
    window.pokedexData = pokedexData; // Make it available globally

    await gameState.initialize();
    await pokemonManager.initialize();
    logger.debug('Pokemon manager and game state initialized');

    // Step 2: Get the first Pokemon in the team (buddy) or create a test Pokemon
    let teamPokemon = pokemonManager.getTeamPokemon();
    if (!teamPokemon || teamPokemon.length === 0) {
      logger.debug('No Pokemon in team, creating a test Pokemon');
      await createTestPokemon();
      teamPokemon = pokemonManager.getTeamPokemon();
    }

    if (!teamPokemon || teamPokemon.length === 0) {
      logger.error('Failed to create test Pokemon, cannot run test');
      return {
        isConsistent: false,
        error: 'No Pokemon in team and failed to create test Pokemon'
      };
    }

    const pokemon = teamPokemon[0];
    logger.debug(`Test Pokemon: ${pokemon.name} (ID: ${pokemon.id})`);
    logger.debug(`Initial state: Level ${pokemon.level}, XP ${pokemon.experience}`);

    // Step 3: Create a wild Pokemon for battle
    const wildPokemon = {
      name: 'Rattata',
      level: 5,
      types: ['normal'],
      rarity: 'common',
      image: './src/PokemonSprites/19.png',
      dex_number: 19
    };

    // Step 4: Calculate battle outcome (force player win)
    // Import the necessary functions from experience-system.js
    const { getExpForLevel, getExpCurveForRarity } = await import('../services/experience-system.js');

    // Calculate how much XP is needed for level up
    const curve = experienceSystem.getExpCurve(pokemon);
    const currentLevel = pokemon.level;
    const nextLevelXp = getExpForLevel(currentLevel + 1, curve);
    const currentXp = pokemon.experience || 0;
    const xpNeededForLevelUp = Math.max(1, nextLevelXp - currentXp);

    // Add a little extra to ensure level up
    const levelUpXp = xpNeededForLevelUp + 50;

    logger.debug(`Current level: ${currentLevel}, Current XP: ${currentXp}, Next level XP: ${nextLevelXp}`);
    logger.debug(`XP needed for level up: ${xpNeededForLevelUp}, Will give: ${levelUpXp}`);

    const battleResult = {
      ...calculateBattleOutcome(pokemon, wildPokemon),
      playerWins: true,
      experienceGained: levelUpXp // Force enough XP gain to level up
    };

    logger.debug(`Battle result: ${battleResult.playerWins ? 'Win' : 'Loss'}, XP gained: ${battleResult.experienceGained}`);

    // Step 5: Record original XP values
    const originalXp = pokemon.experience || 0;
    const xpGained = battleResult.experienceGained;
    const expectedNewXp = originalXp + xpGained;

    logger.debug(`Original XP: ${originalXp}, XP to gain: ${xpGained}, Expected new XP: ${expectedNewXp}`);

    // Step 6: Simulate BattleScreen XP update using the proper method
    logger.debug('=== SIMULATING BATTLESCREEN XP UPDATE ===');

    // Use the addExperience method - this is the ONLY place where XP should be added
    // This simulates what happens in BattleScreen.js's animateExperienceGain method
    const expResult = await pokemon.addExperience(xpGained);
    logger.debug(`Experience result: ${JSON.stringify(expResult)}`);
    logger.debug(`After BattleScreen update: Level ${pokemon.level}, XP ${pokemon.experience}`);

    // Step 7: Reload Pokemon from storage to simulate app state after battle
    await pokemonManager.initialize();
    const reloadedPokemon = pokemonManager.getPokemonById(pokemon.id);

    if (!reloadedPokemon) {
      logger.error(`Failed to reload Pokemon with ID ${pokemon.id}`);
      return {
        isConsistent: false,
        error: `Failed to reload Pokemon with ID ${pokemon.id}`
      };
    }

    logger.debug(`After reload: Level ${reloadedPokemon.level}, XP ${reloadedPokemon.experience}`);

    // Step 8: Simulate EncountersScreen callback and starting a new battle
    // In the fixed version, EncountersScreen no longer adds XP, it just shows level up alerts
    logger.debug('=== SIMULATING ENCOUNTERSSCREEN CALLBACK AND NEW BATTLE START ===');

    // This simulates what happens in EncountersScreen.js after battle
    // It should just get the Pokemon from the manager, not add XP again
    const newBattlePokemon = pokemonManager.getPokemonById(pokemon.id);
    logger.debug(`New battle Pokemon state: Level ${newBattlePokemon.level}, XP ${newBattlePokemon.experience}`);

    // Step 9: Simulate PokemonCaughtScreen view
    logger.debug('=== SIMULATING POKEMONCAUGHTSCREEN VIEW ===');
    // This is how PokemonCaughtScreen loads team Pokemon
    const teamFromStorage = await storageService.get(config.storage.teamPokemonKey, []);
    const teamPokemonInScreen = teamFromStorage.find(p => p.id === pokemon.id);

    if (!teamPokemonInScreen) {
      logger.error(`Pokemon with ID ${pokemon.id} not found in team storage`);
      return {
        isConsistent: false,
        error: `Pokemon with ID ${pokemon.id} not found in team storage`
      };
    }

    logger.debug(`In PokemonCaughtScreen: Level ${teamPokemonInScreen.level}, XP ${teamPokemonInScreen.experience}`);

    // Step 10: Check for inconsistencies
    logger.debug('=== CONSISTENCY CHECK ===');
    const battleScreenXp = pokemon.experience;
    const reloadedXp = reloadedPokemon.experience;
    const newBattleXp = newBattlePokemon.experience;
    const caughtScreenXp = teamPokemonInScreen.experience;

    // Also check level consistency
    const battleScreenLevel = pokemon.level;
    const reloadedLevel = reloadedPokemon.level;
    const newBattleLevel = newBattlePokemon.level;
    const caughtScreenLevel = teamPokemonInScreen.level;

    // Check if level up occurred
    const didLevelUp = battleScreenLevel > currentLevel;

    // Check XP consistency
    const isXpConsistent =
      battleScreenXp === reloadedXp &&
      reloadedXp === newBattleXp &&
      newBattleXp === caughtScreenXp;

    // Check level consistency
    const isLevelConsistent =
      battleScreenLevel === reloadedLevel &&
      reloadedLevel === newBattleLevel &&
      newBattleLevel === caughtScreenLevel;

    // Overall consistency
    const isConsistent = isXpConsistent && isLevelConsistent;

    if (isConsistent) {
      logger.debug('✅ XP AND LEVEL ARE CONSISTENT across all screens and operations');
      if (didLevelUp) {
        logger.debug(`✅ LEVEL UP SUCCESSFUL: ${currentLevel} -> ${battleScreenLevel}`);
      } else {
        logger.debug(`⚠️ NOTE: Level up did not occur as expected. Still at level ${battleScreenLevel}`);
      }
    } else {
      logger.error('❌ INCONSISTENCY DETECTED:');
      logger.error(`Expected XP after battle: ${expectedNewXp}`);
      logger.error(`BattleScreen XP: ${battleScreenXp}`);
      logger.error(`Reloaded XP: ${reloadedXp}`);
      logger.error(`New Battle XP: ${newBattleXp}`);
      logger.error(`PokemonCaughtScreen XP: ${caughtScreenXp}`);

      logger.error(`\nLevels across screens:`);
      logger.error(`Original level: ${currentLevel}`);
      logger.error(`BattleScreen level: ${battleScreenLevel}`);
      logger.error(`Reloaded level: ${reloadedLevel}`);
      logger.error(`New Battle level: ${newBattleLevel}`);
      logger.error(`PokemonCaughtScreen level: ${caughtScreenLevel}`);

      // Identify where the inconsistency occurs
      if (!isXpConsistent) {
        logger.error('\nXP inconsistencies:');
        if (battleScreenXp !== reloadedXp) {
          logger.error(`- BattleScreen vs Reload: ${battleScreenXp} ≠ ${reloadedXp}`);
        }
        if (reloadedXp !== newBattleXp) {
          logger.error(`- Reload vs New Battle: ${reloadedXp} ≠ ${newBattleXp}`);
        }
        if (newBattleXp !== caughtScreenXp) {
          logger.error(`- New Battle vs PokemonCaughtScreen: ${newBattleXp} ≠ ${caughtScreenXp}`);
        }
      }

      if (!isLevelConsistent) {
        logger.error('\nLevel inconsistencies:');
        if (battleScreenLevel !== reloadedLevel) {
          logger.error(`- BattleScreen vs Reload: ${battleScreenLevel} ≠ ${reloadedLevel}`);
        }
        if (reloadedLevel !== newBattleLevel) {
          logger.error(`- Reload vs New Battle: ${reloadedLevel} ≠ ${newBattleLevel}`);
        }
        if (newBattleLevel !== caughtScreenLevel) {
          logger.error(`- New Battle vs PokemonCaughtScreen: ${newBattleLevel} ≠ ${caughtScreenLevel}`);
        }
      }
    }

    logger.debug('=== XP CONSISTENCY TEST COMPLETED ===');

    return {
      isConsistent,
      isXpConsistent,
      isLevelConsistent,
      didLevelUp,
      originalXp,
      xpGained,
      expectedNewXp,
      battleScreenXp,
      reloadedXp,
      newBattleXp,
      caughtScreenXp,
      originalLevel: currentLevel,
      battleScreenLevel,
      reloadedLevel,
      newBattleLevel,
      caughtScreenLevel
    };
  } catch (error) {
    logger.error('Error in XP consistency test:', error);
    return {
      isConsistent: false,
      error: error.message
    };
  }
}
