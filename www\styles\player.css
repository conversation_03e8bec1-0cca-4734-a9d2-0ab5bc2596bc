/* Player sprite styles */
.player-sprite {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: var(--player-size-width, 16px);
  height: var(--player-size-height, 24px);
  z-index: 1000; /* Make sure player is above compass marker */
  pointer-events: none; /* Allow clicks to pass through */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* Player container styles */
.player-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Player icon container styles */
.player-icon-container {
  background: transparent !important;
  border: none !important;
}

/* Animation for walking */
@keyframes playerWalk {
  0% { opacity: 1; }
  33% { opacity: 1; }
  34% { opacity: 0; }
  100% { opacity: 0; }
}

@keyframes playerWalk2 {
  0% { opacity: 0; }
  33% { opacity: 0; }
  34% { opacity: 1; }
  66% { opacity: 1; }
  67% { opacity: 0; }
  100% { opacity: 0; }
}

@keyframes playerWalk3 {
  0% { opacity: 0; }
  66% { opacity: 0; }
  67% { opacity: 1; }
  100% { opacity: 1; }
}

.player-sprite-frame1 {
  animation: playerWalk var(--player-animation-duration, 500ms) steps(1) infinite;
}

.player-sprite-frame2 {
  animation: playerWalk2 var(--player-animation-duration, 500ms) steps(1) infinite;
}

.player-sprite-frame3 {
  animation: playerWalk3 var(--player-animation-duration, 500ms) steps(1) infinite;
}

/* Stop animation when player is not moving */
.player-sprite-static {
  animation: none !important;
}
