// utils/logger.js
// Centralized logging service with support for Capacitor and browser environments

export class Logger {
  constructor(options = {}) {
    this.debugEnabled = options.debug || true; // Set debug mode to true by default
    this.logLevel = options.logLevel || 'debug'; // Set default log level to debug

    // Check for Capacitor environment in multiple ways
    this.useCapacitor = !!(
      (window?.cordova?.plugins?.logger) ||
      (window?.Capacitor?.isNativePlatform && window.Capacitor.isNativePlatform())
    );

    this.levels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };

    // Log initialization for debugging
    if (this.useCapacitor) {
      console.info('Logger initialized in Capacitor environment');
    } else {
      console.info('Logger initialized in browser environment');
    }
  }

  /**
   * Get the current file and line number
   * @returns {Object} - Object with file and line properties
   * @private
   */
  _getCallerInfo() {
    try {
      const err = new Error();
      const stack = err.stack.split('\n');

      // Find the caller in the stack (skip this function and the logger method)
      // Try different stack positions if the expected one doesn't work
      let callerLine = '';
      for (let i = 3; i < Math.min(stack.length, 6); i++) {
        if (stack[i] && !stack[i].includes('logger.js')) {
          callerLine = stack[i];
          break;
        }
      }

      if (!callerLine) {
        return { file: 'unknown', line: 0 };
      }

      // Extract file and line information
      const match = callerLine.match(/at\s+(.+?)\s+\((.+?):(\d+):(\d+)\)/);
      if (match) {
        return {
          function: match[1],
          file: match[2],
          line: parseInt(match[3], 10),
          column: parseInt(match[4], 10)
        };
      }

      // Alternative format without function name
      const altMatch = callerLine.match(/at\s+(.+?):(\d+):(\d+)/);
      if (altMatch) {
        return {
          file: altMatch[1],
          line: parseInt(altMatch[2], 10),
          column: parseInt(altMatch[3], 10)
        };
      }

      // Capacitor-specific format (might be different)
      const capacitorMatch = callerLine.match(/at\s+(.+)/);
      if (capacitorMatch) {
        return {
          file: capacitorMatch[1],
          line: 0,
          column: 0
        };
      }
    } catch (e) {
      // Ignore errors in stack trace parsing
    }

    return { file: 'unknown', line: 0 };
  }

  /**
   * Log a debug message
   * @param {string} message - The message to log
   * @param  {...any} args - Additional arguments
   */
  debug(message, ...args) {
    if (this._shouldLog('debug')) {
      const callerInfo = this._getCallerInfo();
      // Handle undefined message
      const safeMessage = message !== undefined ? message : '[undefined message]';

      if (this.useCapacitor) {
        // For Capacitor, include file and line in the message
        const formattedMessage = `[${callerInfo.file}:${callerInfo.line}] ${safeMessage}`;
        try {
          window.cordova.plugins.logger.debug(this._format(formattedMessage, args));
        } catch (e) {
          // Fallback to console if Capacitor logger fails
          console.debug(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
        }
      } else {
        console.debug(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
      }
    }
  }

  /**
   * Log an info message
   * @param {string} message - The message to log
   * @param  {...any} args - Additional arguments
   */
  info(message, ...args) {
    if (this._shouldLog('info')) {
      const callerInfo = this._getCallerInfo();
      // Handle undefined message
      const safeMessage = message !== undefined ? message : '[undefined message]';

      if (this.useCapacitor) {
        // For Capacitor, include file and line in the message
        const formattedMessage = `[${callerInfo.file}:${callerInfo.line}] ${safeMessage}`;
        try {
          window.cordova.plugins.logger.info(this._format(formattedMessage, args));
        } catch (e) {
          // Fallback to console if Capacitor logger fails
          console.info(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
        }
      } else {
        console.info(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
      }
    }
  }

  /**
   * Log a warning message
   * @param {string} message - The message to log
   * @param  {...any} args - Additional arguments
   */
  warn(message, ...args) {
    if (this._shouldLog('warn')) {
      const callerInfo = this._getCallerInfo();
      // Handle undefined message
      const safeMessage = message !== undefined ? message : '[undefined message]';

      if (this.useCapacitor) {
        // For Capacitor, include file and line in the message
        const formattedMessage = `[${callerInfo.file}:${callerInfo.line}] ${safeMessage}`;
        try {
          window.cordova.plugins.logger.warn(this._format(formattedMessage, args));
        } catch (e) {
          // Fallback to console if Capacitor logger fails
          console.warn(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
        }
      } else {
        console.warn(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
      }
    }
  }

  /**
   * Log an error message
   * @param {string} message - The message to log
   * @param  {...any} args - Additional arguments
   */
  error(message, ...args) {
    if (this._shouldLog('error')) {
      const callerInfo = this._getCallerInfo();
      // Handle undefined message
      const safeMessage = message !== undefined ? message : '[undefined message]';

      if (this.useCapacitor) {
        // For Capacitor, include file and line in the message
        const formattedMessage = `[${callerInfo.file}:${callerInfo.line}] ${safeMessage}`;
        try {
          window.cordova.plugins.logger.error(this._format(formattedMessage, args));
        } catch (e) {
          // Fallback to console if Capacitor logger fails
          console.error(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
        }
      } else {
        console.error(`[${callerInfo.file}:${callerInfo.line}]`, safeMessage, ...args);
      }
    }
  }

  /**
   * Check if the given level should be logged
   * @param {string} level - The log level to check
   * @returns {boolean} - Whether the level should be logged
   * @private
   */
  _shouldLog(level) {
    return this.levels[level] >= this.levels[this.logLevel];
  }

  /**
   * Format a message and arguments for logging
   * @param {string} message - The message to format
   * @param {Array} args - Additional arguments
   * @returns {string} - The formatted message
   * @private
   */
  _format(message, args) {
    // Ensure message is a string
    const safeMessage = message !== undefined ? String(message) : '[undefined message]';

    if (!args || args.length === 0) return safeMessage;

    try {
      return `${safeMessage} ${args.map(arg => {
        if (arg === null) return 'null';
        if (arg === undefined) return 'undefined';

        if (typeof arg === 'object') {
          // Handle arrays
          if (Array.isArray(arg)) {
            if (arg.length === 0) return '[]';
            if (arg.length <= 3) {
              return `[${arg.map(item => this._formatValue(item)).join(', ')}]`;
            }
            return `Array(${arg.length}) [${arg.slice(0, 3).map(item => this._formatValue(item)).join(', ')}, ...]`;
          }

          // Handle objects
          try {
            const keys = Object.keys(arg);
            if (keys.length === 0) return '{}';

            // For small objects, show all properties
            if (keys.length <= 3) {
              return `{ ${keys.map(key => `${key}: ${this._formatValue(arg[key])}`).join(', ')} }`;
            }

            // For larger objects, show summary with count and first few properties
            return `Object(${keys.length}) { ${keys.slice(0, 3).map(key =>
              `${key}: ${this._formatValue(arg[key])}`
            ).join(', ')}, ... }`;
          } catch (e) {
            return '[Complex Object]';
          }
        }

        return String(arg);
      }).join(' ')}`;
    } catch (e) {
      return `${safeMessage} [Unserializable args]`;
    }
  }

  /**
   * Format a single value for logging
   * @param {any} value - The value to format
   * @returns {string} - The formatted value
   * @private
   */
  _formatValue(value) {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';

    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        if (value.length === 0) return '[]';
        return `[Array(${value.length})]`;
      }
      try {
        const keys = Object.keys(value);
        return `{Object(${keys.length})}`;
      } catch (e) {
        return '{Object}';
      }
    }

    return String(value);
  }

  /**
   * Set the debug mode
   * @param {boolean} enabled - Whether debug mode is enabled
   */
  setDebugEnabled(enabled) {
    this.debugEnabled = enabled;
  }

  /**
   * Set the log level
   * @param {string} level - The log level (debug, info, warn, error)
   */
  setLogLevel(level) {
    if (this.levels[level] !== undefined) {
      this.logLevel = level;
    }
  }
}

// Export a singleton instance
export const logger = new Logger();
