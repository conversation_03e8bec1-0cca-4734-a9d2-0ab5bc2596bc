// fetch-all-with-details.js
// Script to download data for the first 151 Pokémon, including name, dex number, family (evolution chain), types, and image URLs.

import fs from "fs/promises";
import fetch from "node-fetch";

// Helper to fetch and parse JSON
async function fetchJson(url) {
  const res = await fetch(url);
  if (!res.ok) throw new Error(`Fetch error ${res.status} for ${url}`);
  return res.json();
}

async function main() {
  const result = [];
  for (let id = 1; id <= 151; id++) {
    try {
      // 1) Pokémon species (to get evolution chain URL and official names)
      const speciesUrl = `https://pokeapi.co/api/v2/pokemon-species/${id}/`;
      const species = await fetchJson(speciesUrl);
      const chainUrl = species.evolution_chain.url;
      const chainIdMatch = chainUrl.match(/\/evolution-chain\/(\d+)\//);
      const chainId = chainIdMatch ? Number(chainIdMatch[1]) : null;

      // 2) Evolution chain (to get the base species name)
      let baseName = species.name; // fallback
      if (chainId) {
        const chain = await fetchJson(`https://pokeapi.co/api/v2/evolution-chain/${chainId}/`);
        baseName = chain.chain.species.name;
      }

      // 3) Pokémon data (to get types, images, and dex number)
      const pokeUrl = `https://pokeapi.co/api/v2/pokemon/${id}/`;
      const pokeData = await fetchJson(pokeUrl);

      // Extract types
      const types = pokeData.types
        .sort((a, b) => a.slot - b.slot)
        .map(entry => entry.type.name);

      // Extract official artwork, fallback to front_default
      const image = (pokeData.sprites.other &&
                     pokeData.sprites.other['official-artwork'] &&
                     pokeData.sprites.other['official-artwork'].front_default)
        || pokeData.sprites.front_default;

      // Pack everything
      result.push({
        id: species.id,
        dex_number: species.id,
        name: species.name,
        evolution_chain_id: chainId,
        base_species: baseName,
        types,
        image_url: image
      });

      console.log(`Fetched #${id}: ${species.name}`);
    } catch (err) {
      console.error(`Error fetching ID ${id}:`, err);
    }
  }

  // Save to disk
  const filename = "pokedex-1-151-full.json";
  await fs.writeFile(filename, JSON.stringify(result, null, 2), "utf-8");
  console.log(`Saved ${result.length} entries to ${filename}`);
}

main().catch(console.error);
