/* battle-screen.css */
.battle-overlay {
  background: var(--standard-background-color, #f4f4f4);
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  z-index: 10020;
  overflow-y: auto;
  padding: 0;
}

.battle-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  padding: 0;
  overflow: auto; /* Allow scrolling if needed */
  background-color: #f8f8f8; /* Light background for the entire battle area */
}

.battle-title {
  text-align: center;
  padding: 5px 0; /* Reduced from 10px */
  margin-bottom: 3px; /* Reduced from 5px */
}

.battle-title h2 {
  font-size: 1.3rem; /* Reduced from 1.5rem */
  font-weight: bold;
  margin: 0;
  color: #333;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.battle-arena {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1; /* Take remaining space */
  background: none;
  position: relative;
  overflow: hidden;
  height: 60vh; /* <PERSON><PERSON> Höhe für den Kampfbereich */
}

/* Battle background image */
.battle-background {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1 / 1;
  z-index: 0; /* Behind everything */
  object-fit: contain; /* Keep aspect ratio and show full image */
  object-position: center; /* Center the image */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  flex-direction: row-reverse;
}



.battle-results {
  background-color: white;
  padding: 8px; /* Reduced from 10px */
  text-align: center;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  z-index: 5;
  font-size: 1rem; /* Reduced from 1.1rem */
  font-weight: 500;
  min-height: 50px; /* Reduced from 60px */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  order: 0; /* In portrait mode, results appear between wild and player */
  margin: 5px; /* Reduced from 10px */
  border-radius: 10px;
}

.battle-exp-gain {
  margin-top: 8px;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 3s forwards; /* Appear after damage calculation */
}

.exp-gain-text {
  color: #4a90e2; /* Blue color for experience */
  font-weight: bold;
  font-size: 0.9rem;
  background-color: rgba(74, 144, 226, 0.1);
  padding: 3px 10px;
  border-radius: 12px;
  display: inline-block;
}

.battle-calculation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 2.3s forwards; /* Appear slightly after the result text */
}



.battle-calculation-vs {
  margin: 0 10px;
  font-weight: bold;
  animation: pulse 1.5s infinite 2.5s; /* Start pulsing after appearing */
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.player-level, .wild-level {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  color: white;
  opacity: 0;
  transform: scale(0);
}

.player-level {
  background-color: #f14b3d; /* Red background for player */
  animation: popIn 0.4s ease-out 2.5s forwards; /* Pop in after VS appears */
}

.wild-level {
  background-color: #595f65; /* Grey background for wild */
  animation: popIn 0.4s ease-out 2.7s forwards; /* Pop in slightly after player level */
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  70% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.battle-info-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px;
  position: relative;
  top: 0;
  left: 0;
  z-index: 3;
}

.battle-arena {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.player-side {
  position: relative;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%; /* Nimmt die untere Hälfte des Kampfbereichs ein */
  display: flex;
  justify-content: flex-start; /* Align to left */
  align-items: flex-start; /* Align to top */
  padding: 0;
  overflow: hidden;
  background: none;
}

.wild-side {
  position: relative;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%; /* Nimmt die obere Hälfte des Kampfbereichs ein */
  display: flex;
  justify-content: flex-end; /* Align to right */
  align-items: flex-end; /* Align to bottom */
  padding: 0;
  overflow: hidden;
  background: none;
}

/* Media query for landscape mode */
@media screen and (orientation: landscape) {
  .battle-container {
    flex-direction: column;
  }

  .battle-arena {
    height: 40vh; /* Kleinere Höhe im Landscape-Modus */
  }

  .battle-results {
    flex: 1;
    order: 2;
    position: static;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    min-height: 0;
  }

  /* Positionierung der Pokémon im Landscape-Modus */
  .player-pokemon {
    left: 15%;
    top: 15%; /* Im oberen Bereich des player-side Containers */
  }

  .wild-pokemon {
    right: 15%;
    bottom: 15%; /* Im unteren Bereich des wild-side Containers */
  }

  .pokemon-battle-card {
    max-width: 160px; /* Reduced from 180px */
  }
}

.pokemon-battle-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 8px; /* Reduced from 10px */
  width: 160px; /* Reduced from 180px */
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  z-index: 2; /* Ensure card is above the image and background */
  margin: 0 5px; /* Added horizontal margin for spacing */
  position: relative; /* Needed for z-index to work */
  height: 100%;
  flex: 1;
  max-width: 280px;
}

.player-card {
  align-self: flex-start;
}

.wild-card {
  align-self: flex-end;
}

.pokemon-battle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.pokemon-battle-name {
  font-weight: 600;
  color: var(--standard-text-color);
  font-size: 0.8rem;
}

.pokemon-battle-level {
  font-size: 0.7rem;
  color: #666;
  margin-left: 5px;
}

.pokemon-health-container {
  margin: 5px 0 8px 0;
}

.pokemon-health-bar {
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 2px;
}

.pokemon-health-fill {
  height: 100%;
  background-color: #4CAF50; /* Green for health */
  border-radius: 5px;
  transition: width 1s ease-in-out, background-color 0.5s ease-in-out;
}

/* Low health style (red) */
.low-health {
  background-color: #f44336 !important; /* Red for low health */
}

.pokemon-health-text {
  font-size: 0.7rem;
  color: #666;
  text-align: right;
}

/* Experience bar styling */
.pokemon-exp-container {
  margin: 2px 0;
  width: 100%;
}

.pokemon-exp-bar {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 2px;
  position: relative; /* Added for absolute positioning of children */
}

.pokemon-exp-fill {
  height: 100%;
  background-color: #4a90e2; /* Blue for experience */
  border-radius: 3px;
  transition: width 0.5s ease-out;
  position: relative; /* Added for z-index */
  z-index: 1; /* Ensure it's above the new exp */
}

.pokemon-exp-new {
  height: 100%;
  background-color: #4a90e2; /* Same blue but with opacity */
  border-radius: 3px;
  opacity: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  transition: opacity 0.3s ease-in;
  z-index: 2; /* Above the fill */
}

.pokemon-exp-text {
  font-size: 0.7rem;
  color: #666;
  text-align: right;
}

.level-up-notification {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 215, 0, 0.9);
  color: #333;
  font-weight: bold;
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.7);
  animation: levelUpGlow 1.5s infinite;
  opacity: 0;
  z-index: 10;
  transition: opacity 0.5s ease-in;
}

@keyframes levelUpGlow {
  0% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.7);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 215, 0, 1);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.7);
  }
}

.pokemon-battle-types {
  display: flex;
  gap: 3px;
  margin-top: 5px;
  flex-wrap: wrap; /* Allow type badges to wrap on small screens */
  align-items: center;
}

/* Style for each type badge group */
.pokemon-type-badge-group {
  display: flex;
  align-items: center;
  margin-right: 5px;
  margin-bottom: 3px;
}

/* Type effectiveness styling */
.pokemon-type-badge-group > .pokemon-type-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
  font-size: 0.6rem; /* Verkleinert, um mit type-effectiveness übereinzustimmen */
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0,0,0,0.3);
  box-shadow: 0 1px 2px rgba(0,0,0,0.2);
  margin-right: 2px;
}

.type-effectiveness {
  display: inline-block;
  font-size: 0.6rem; /* Angepasst an die Größe der type-badge */
  padding: 1px 3px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
  margin-right: 2px;
  margin-left: 2px;
}

.type-super {
  color: #2e7d32; /* Green for super effective */
  background-color: rgba(46, 125, 50, 0.2);
}

.type-not-very {
  color: #c62828; /* Red for not very effective */
  background-color: rgba(198, 40, 40, 0.2);
}

.type-no-effect {
  color: #424242; /* Dark gray for no effect */
  background-color: rgba(66, 66, 66, 0.2);
}

.type-neutral {
  color: #1976d2; /* Blue for neutral */
  background-color: rgba(25, 118, 210, 0.2);
}

.pokemon-battle-image {
  width: auto; /* Reduced from 120px */
  height: auto; /* Reduced from 120px */
  object-fit: contain;
  transition: all 0.3s ease; /* Smooth transition for size changes */
  max-width: 90%; /* Ensure image doesn't overflow */
  position: relative;
  z-index: 1; /* Above the background but below the cards */
}

.player-pokemon {
  position: absolute;
  left: 25%; /* Positionierung von links */
  bottom: 18%; /* Im oberen Bereich des player-side Containers */
  transform: scaleX(-1); /* Flip the player Pokemon to face the opponent */
}

.wild-pokemon {
  position: absolute;
  right: 20%; /* Positionierung von rechts */
  bottom: 34%; /* Im unteren Bereich des wild-side Containers */
}

/* Animations */
@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%) scaleX(-1);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scaleX(-1);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes healthDecrease {
  0% {
    width: 100%;
  }
  100% {
    width: var(--final-health, 15%);
  }
}

/* Animations für die Pokémon werden direkt in den Klassen .player-pokemon und .wild-pokemon definiert */

.battle-results {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 2s forwards; /* 2s delay before showing results */
}

.battle-title {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.5s forwards; /* Appear after Pokemon */
}

/* Apply shake animation to the defeated Pokemon */
.player-defeated .player-pokemon {
  animation: shake 0.5s ease-in-out 1.5s;
}

.wild-defeated .wild-pokemon {
  animation: shake 0.5s ease-in-out 1.5s;
}

/* Health bar animations */
.player-defeated .player-card .pokemon-health-fill {
  --final-health: 5%;
  animation: healthDecrease 1.5s ease-in-out 1s forwards;
}

.wild-defeated .wild-card .pokemon-health-fill {
  --final-health: 5%;
  animation: healthDecrease 1.5s ease-in-out 1s forwards;
}

/* Health bar animations for non-defeated Pokemon */
.player-side:not(.player-defeated) ~ .battle-info-container .player-card .pokemon-health-fill {
  --final-health: 90%;
  animation: healthDecrease 1.5s ease-in-out 1s forwards;
}

.wild-side:not(.wild-defeated) ~ .battle-info-container .wild-card .pokemon-health-fill {
  --final-health: 90%;
  animation: healthDecrease 1.5s ease-in-out 1s forwards;
}

/* Media query for landscape mode - adjust image sizes */
@media screen and (orientation: landscape) {
  .pokemon-battle-image {
    width: 80px;
    height: 80px;
  }

  .pokemon-battle-card {
    width: 160px;
    padding: 8px;
  }

  .pokemon-battle-name {
    font-size: 0.9rem;
  }

  .pokemon-battle-level {
    font-size: 0.7rem;
  }

  .pokemon-battle-types {
    gap: 2px;
    margin-top: 2px;
  }

  .pokemon-type-badge-group > .pokemon-type-badge {
    padding: 1px 6px;
    font-size: 0.8em;
  }

  .battle-title h2 {
    font-size: 1.3rem;
  }

  .pokemon-health-bar {
    height: 8px;
  }
}

/* Media query for smaller screens */
@media screen and (max-height: 500px) {
  .pokemon-battle-image {
    width: 60px; /* Reduced from 70px */
    height: 60px; /* Reduced from 70px */
  }

  .battle-arena {
    height: 50vh; /* Kleinere Höhe für kleine Bildschirme */
  }

  /* Positionierung der Pokémon für kleine Bildschirme */
  .player-pokemon {
    left: 5%;
    top: 5%; /* Im oberen Bereich des player-side Containers */
  }

  .wild-pokemon {
    right: 5%;
    bottom: 5%; /* Im unteren Bereich des wild-side Containers */
  }

  .pokemon-battle-card {
    width: 150px; /* Smaller cards for small screens */
    padding: 5px; /* Reduced padding */
  }

  .battle-results {
    min-height: 50px;
    padding: 5px;
    font-size: 0.9rem;
  }

  .battle-title {
    padding: 5px 0;
  }

  .battle-title h2 {
    font-size: 1.2rem;
  }

  .pokemon-battle-card {
    width: 150px;
    padding: 8px;
  }


}

/* Battle results styling */
.battle-victory {
  color: #2e7d32; /* Green color for victory */
  font-weight: bold;
}

.battle-defeat {
  color: #c62828; /* Red color for defeat */
  font-weight: bold;
}

/* Tie battle results styling */
.battle-tie-victory {
  color: #1976d2; /* Blue color for tie victory */
  font-weight: bold;
  font-style: italic;
}

.battle-tie-defeat {
  color: #e65100; /* Orange color for tie defeat */
  font-weight: bold;
  font-style: italic;
}

.error-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #c62828;
  font-weight: bold;
  text-align: center;
  padding: 20px;
}

/* Additional styles for battle calculation in landscape mode */
@media screen and (orientation: landscape) {
  .battle-calculation {
    margin-top: 5px;
  }

  .player-level, .wild-level {
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 0.8rem;
  }
}

/* Battle controls section */
.battle-controls {
  display: flex;
  flex-direction: column;
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #ddd;
  border-radius: 10px;
  margin: 0 10px 10px 10px;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.battle-move-selector {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 5px 10px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.battle-move-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 10px;
}

.battle-move-btn {
  background-color: var(--button-color, #4CAF50);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 15px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  text-align: center;
}

.continue-fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--button-color, #4CAF50);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10030; /* Above everything */
  opacity: 0;
  animation: fadeIn 0.5s ease-out 3s forwards; /* Appear after battle result */
}

.continue-fab img {
  width: 24px;
  height: 24px;
}

.white-icon {
  filter: brightness(0) invert(1); /* Macht das Icon weiß */
}

.continue-fab:hover {
  background-color: var(--button-hover-color, #45a049);
}

.continue-fab:active {
  transform: translateY(2px);
  box-shadow: 0 1px 3px rgba(0,0,0,0.16), 0 1px 3px rgba(0,0,0,0.23);
}

.battle-move-btn.selected {
  background-color: #2196F3; /* Blue for selected button */
  box-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
}
