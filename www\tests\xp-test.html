<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pokemon XP Consistency Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 10px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .success {
      color: green;
      font-weight: bold;
    }
    .error {
      color: red;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>Pokemon XP Consistency Test</h1>
  <p>This test checks for XP consistency issues across different screens and operations.</p>

  <div style="background-color: #e6f7ff; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
    <h3>Fix Information</h3>
    <p>The XP inconsistency issue has been fixed by ensuring XP is added only once per battle:</p>
    <ul>
      <li>XP is now added <strong>only</strong> in BattleScreen.js's animateExperienceGain method</li>
      <li>EncountersScreen.js no longer adds XP, it only shows level up alerts</li>
      <li>This prevents the double XP gain that was causing inconsistencies</li>
    </ul>
  </div>

  <button id="run-test">Run XP Consistency Test</button>
  <button id="clear-results">Clear Results</button>

  <h2>Test Results:</h2>
  <pre id="test-results">No test has been run yet.</pre>

  <script type="module">
    import { testXpConsistency } from './xp-consistency-test.js';
    import { gameState } from '../state/game-state.js';
    import { logger } from '../utils/logger.js';

    // Load pokedex data when the page loads
    async function loadPokedexData() {
      try {
        const resultsElement = document.getElementById('test-results');
        resultsElement.textContent = 'Loading pokedex data...';

        // Try to load pokedex data directly
        try {
          const response = await fetch('../pokedex-151.json');
          if (!response.ok) {
            throw new Error(`Failed to load pokedex data: ${response.status} ${response.statusText}`);
          }

          const pokedexData = await response.json();
          console.log(`Loaded ${pokedexData.length} Pokemon from pokedex data`);

          // Make it available globally
          window.pokedexData = pokedexData;

          // Initialize game state
          await gameState.initialize();

          resultsElement.textContent = `Pokedex data loaded (${pokedexData.length} Pokemon). Ready to run test.`;
        } catch (error) {
          console.error('Error loading pokedex data directly:', error);

          // Fall back to game state initialization
          await gameState.initialize();

          resultsElement.textContent = 'Game state initialized. Ready to run test.';
        }
      } catch (error) {
        console.error('Error loading data:', error);
        document.getElementById('test-results').textContent = `Error loading data: ${error.message}`;
        document.getElementById('test-results').className = 'error';
      }
    }

    // Load data when the page loads
    window.addEventListener('DOMContentLoaded', loadPokedexData);

    document.getElementById('run-test').addEventListener('click', async () => {
      const resultsElement = document.getElementById('test-results');
      resultsElement.textContent = 'Running test...';

      try {
        // Run the test
        const results = await testXpConsistency();

        let output = '';
        if (results.isConsistent) {
          output += '✅ SUCCESS: XP and level are consistent across all screens and operations\n\n';
          if (results.didLevelUp) {
            output += `✅ LEVEL UP SUCCESSFUL: ${results.originalLevel} -> ${results.battleScreenLevel}\n\n`;
          } else {
            output += `⚠️ NOTE: Level up did not occur as expected. Still at level ${results.battleScreenLevel}\n\n`;
          }
        } else if (results.error) {
          output += `❌ ERROR: ${results.error}\n\n`;
        } else {
          output += '❌ ERROR: Inconsistency detected\n\n';
        }

        output += 'Test Details:\n';
        output += `- Original XP: ${results.originalXp}\n`;
        output += `- XP Gained: ${results.xpGained}\n`;
        output += `- Expected New XP: ${results.expectedNewXp}\n\n`;

        output += 'XP Values Across Screens:\n';
        output += `- BattleScreen XP: ${results.battleScreenXp}\n`;
        output += `- Reloaded XP: ${results.reloadedXp}\n`;
        output += `- New Battle XP: ${results.newBattleXp}\n`;
        output += `- PokemonCaughtScreen XP: ${results.caughtScreenXp}\n\n`;

        output += 'Level Values Across Screens:\n';
        output += `- Original Level: ${results.originalLevel}\n`;
        output += `- BattleScreen Level: ${results.battleScreenLevel}\n`;
        output += `- Reloaded Level: ${results.reloadedLevel}\n`;
        output += `- New Battle Level: ${results.newBattleLevel}\n`;
        output += `- PokemonCaughtScreen Level: ${results.caughtScreenLevel}\n\n`;

        if (!results.isConsistent && !results.error) {
          output += 'Inconsistencies:\n';

          if (!results.isXpConsistent) {
            output += 'XP inconsistencies:\n';
            if (results.battleScreenXp !== results.reloadedXp) {
              output += `- BattleScreen vs Reload: ${results.battleScreenXp} ≠ ${results.reloadedXp}\n`;
            }
            if (results.reloadedXp !== results.newBattleXp) {
              output += `- Reload vs New Battle: ${results.reloadedXp} ≠ ${results.newBattleXp}\n`;
            }
            if (results.newBattleXp !== results.caughtScreenXp) {
              output += `- New Battle vs PokemonCaughtScreen: ${results.newBattleXp} ≠ ${results.caughtScreenXp}\n`;
            }
            output += '\n';
          }

          if (!results.isLevelConsistent) {
            output += 'Level inconsistencies:\n';
            if (results.battleScreenLevel !== results.reloadedLevel) {
              output += `- BattleScreen vs Reload: ${results.battleScreenLevel} ≠ ${results.reloadedLevel}\n`;
            }
            if (results.reloadedLevel !== results.newBattleLevel) {
              output += `- Reload vs New Battle: ${results.reloadedLevel} ≠ ${results.newBattleLevel}\n`;
            }
            if (results.newBattleLevel !== results.caughtScreenLevel) {
              output += `- New Battle vs PokemonCaughtScreen: ${results.newBattleLevel} ≠ ${results.caughtScreenLevel}\n`;
            }
          }
        }

        resultsElement.textContent = output;
        resultsElement.className = results.isConsistent ? 'success' : 'error';
      } catch (error) {
        resultsElement.textContent = `Error running test: ${error.message}`;
        resultsElement.className = 'error';
        console.error('Test error:', error);
      }
    });

    document.getElementById('clear-results').addEventListener('click', () => {
      document.getElementById('test-results').textContent = 'No test has been run yet.';
      document.getElementById('test-results').className = '';
    });
  </script>
</body>
</html>
